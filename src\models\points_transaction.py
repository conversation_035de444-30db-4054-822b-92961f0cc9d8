#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分交易记录模型
用于记录用户的积分变动历史
"""

import os
import json
import uuid
from datetime import datetime
from typing import Optional, List


class PointsTransaction:
    """积分交易记录类"""
    
    def __init__(self, transaction_id: str, user_id: str, amount: int, 
                 transaction_type: str, description: str = "", related_id: str = None):
        self.transaction_id = transaction_id
        self.user_id = user_id
        self.amount = amount  # 正数为增加，负数为扣除
        self.transaction_type = transaction_type  # 'signin', 'translation', 'register', 'purchase'
        self.description = description
        self.related_id = related_id  # 关联的任务ID或其他ID
        self.created_at = datetime.now()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'transaction_id': self.transaction_id,
            'user_id': self.user_id,
            'amount': self.amount,
            'transaction_type': self.transaction_type,
            'description': self.description,
            'related_id': self.related_id,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建交易记录对象"""
        transaction = cls(
            data['transaction_id'],
            data['user_id'],
            data['amount'],
            data['transaction_type'],
            data.get('description', ''),
            data.get('related_id')
        )
        transaction.created_at = datetime.fromisoformat(data['created_at'])
        return transaction
    
    def save(self):
        """保存交易记录"""
        os.makedirs('data/transactions', exist_ok=True)
        file_path = f'data/transactions/{self.transaction_id}.json'
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load(cls, transaction_id: str) -> Optional['PointsTransaction']:
        """加载交易记录"""
        file_path = f'data/transactions/{transaction_id}.json'
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except Exception:
            return None
    
    @classmethod
    def get_user_transactions(cls, user_id: str, limit: int = 50) -> List['PointsTransaction']:
        """获取用户的交易记录"""
        transactions = []
        transactions_dir = 'data/transactions'
        
        if not os.path.exists(transactions_dir):
            return transactions
        
        # 获取所有交易记录文件
        for filename in os.listdir(transactions_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(transactions_dir, filename), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if data.get('user_id') == user_id:
                        transaction = cls.from_dict(data)
                        transactions.append(transaction)
                except Exception:
                    continue
        
        # 按时间倒序排列
        transactions.sort(key=lambda x: x.created_at, reverse=True)
        
        # 限制返回数量
        return transactions[:limit]
    
    @classmethod
    def create_transaction(cls, user_id: str, amount: int, transaction_type: str, 
                          description: str = "", related_id: str = None) -> 'PointsTransaction':
        """创建新的交易记录"""
        transaction_id = str(uuid.uuid4())
        transaction = cls(transaction_id, user_id, amount, transaction_type, description, related_id)
        transaction.save()
        return transaction
    
    @classmethod
    def create_signin_transaction(cls, user_id: str) -> 'PointsTransaction':
        """创建签到交易记录"""
        return cls.create_transaction(
            user_id=user_id,
            amount=10,
            transaction_type='signin',
            description='每日签到奖励'
        )
    
    @classmethod
    def create_translation_transaction(cls, user_id: str, job_id: str) -> 'PointsTransaction':
        """创建翻译交易记录"""
        return cls.create_transaction(
            user_id=user_id,
            amount=-50,
            transaction_type='translation',
            description='书籍翻译费用',
            related_id=job_id
        )
    
    @classmethod
    def create_register_transaction(cls, user_id: str) -> 'PointsTransaction':
        """创建注册奖励交易记录"""
        return cls.create_transaction(
            user_id=user_id,
            amount=100,
            transaction_type='register',
            description='新用户注册奖励'
        )
