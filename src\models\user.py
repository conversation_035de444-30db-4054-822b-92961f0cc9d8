#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户模型
"""

import json
import os
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional
import jwt


class User:
    """用户类"""

    def __init__(self, user_id: str, email: str, password_hash: str = None, points_balance: int = 100):
        self.user_id = user_id
        self.email = email
        self.password_hash = password_hash
        self.points_balance = points_balance
        self.created_at = datetime.now()
        self.last_signin = None
        self.is_active = True
        self.email_verified = False
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'user_id': self.user_id,
            'email': self.email,
            'points_balance': self.points_balance,
            'created_at': self.created_at.isoformat(),
            'last_signin': self.last_signin.isoformat() if self.last_signin else None,
            'is_active': self.is_active,
            'email_verified': self.email_verified
        }

        if include_sensitive:
            data['password_hash'] = self.password_hash

        return data
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建用户对象"""
        user = cls(
            data['user_id'],
            data['email'],
            data.get('password_hash'),
            data.get('points_balance', 100)
        )
        user.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('last_signin'):
            user.last_signin = datetime.fromisoformat(data['last_signin'])
        user.is_active = data.get('is_active', True)
        user.email_verified = data.get('email_verified', False)
        return user
    
    def save(self):
        """保存用户数据"""
        os.makedirs('data/users', exist_ok=True)
        file_path = f'data/users/{self.user_id}.json'
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(include_sensitive=True), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load(cls, user_id: str) -> Optional['User']:
        """加载用户数据"""
        file_path = f'data/users/{user_id}.json'
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls.from_dict(data)
        except Exception:
            return None
    
    def deduct_points(self, amount: int, transaction_type: str = 'deduct',
                     description: str = '', related_id: str = None) -> bool:
        """扣除积分并记录交易"""
        if self.points_balance >= amount:
            self.points_balance -= amount

            # 记录交易
            from src.models.points_transaction import PointsTransaction
            PointsTransaction.create_transaction(
                user_id=self.user_id,
                amount=-amount,
                transaction_type=transaction_type,
                description=description,
                related_id=related_id
            )

            return True
        return False

    def add_points(self, amount: int, transaction_type: str = 'add',
                  description: str = '', related_id: str = None):
        """增加积分并记录交易"""
        self.points_balance += amount

        # 记录交易
        from src.models.points_transaction import PointsTransaction
        PointsTransaction.create_transaction(
            user_id=self.user_id,
            amount=amount,
            transaction_type=transaction_type,
            description=description,
            related_id=related_id
        )

    def daily_signin(self) -> bool:
        """每日签到"""
        today = datetime.now().date()
        if self.last_signin and self.last_signin.date() == today:
            return False  # 今天已经签到过了

        self.add_points(10, 'signin', '每日签到奖励')  # 签到奖励10积分
        self.last_signin = datetime.now()
        self.save()
        return True

    # 认证相关方法
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        import hashlib
        import secrets
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
        return f"{salt}:{password_hash.hex()}"

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        if not self.password_hash:
            return False

        import hashlib
        try:
            salt, stored_hash = self.password_hash.split(':')
            password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
            return password_hash.hex() == stored_hash
        except ValueError:
            return False

    def generate_jwt_token(self, secret_key: str, expires_in_hours: int = 24) -> str:
        """生成JWT令牌"""
        import jwt
        from datetime import timedelta

        payload = {
            'user_id': self.user_id,
            'email': self.email,
            'exp': datetime.utcnow() + timedelta(hours=expires_in_hours),
            'iat': datetime.utcnow()
        }
        return jwt.encode(payload, secret_key, algorithm='HS256')

    @staticmethod
    def verify_jwt_token(token: str, secret_key: str) -> Optional[dict]:
        """验证JWT令牌"""
        import jwt
        try:
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    @classmethod
    def find_by_email(cls, email: str) -> Optional['User']:
        """通过邮箱查找用户"""
        users_dir = 'data/users'
        if not os.path.exists(users_dir):
            return None

        for filename in os.listdir(users_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(users_dir, filename), 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if data.get('email') == email:
                        return cls.from_dict(data)
                except Exception:
                    continue
        return None

    @classmethod
    def create_user(cls, email: str, password: str) -> 'User':
        """创建新用户"""
        import uuid
        user_id = str(uuid.uuid4())
        password_hash = cls.hash_password(password)
        user = cls(user_id, email, password_hash, 100)  # 新用户赠送100积分
        user.save()

        # 记录注册奖励交易
        from src.models.points_transaction import PointsTransaction
        PointsTransaction.create_register_transaction(user_id)

        return user
