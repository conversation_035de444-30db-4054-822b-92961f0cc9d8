#!/usr/bin/env python3
"""
测试PDF翻译功能
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:5000"

def test_pdf_translation():
    """测试PDF翻译功能"""
    print("📄 测试PDF翻译功能")
    print("=" * 50)
    
    # 1. 先登录获取token
    print("1️⃣ 用户登录...")
    login_data = {
        "email": "<EMAIL>",
        "password": "Test123456"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            # 如果登录失败，尝试注册
            print("   登录失败，尝试注册...")
            register_response = requests.post(
                f"{BASE_URL}/api/auth/register",
                json=login_data,
                headers={'Content-Type': 'application/json'}
            )
            if register_response.status_code == 201:
                result = register_response.json()
                token = result['token']
                print("   ✅ 注册成功")
            else:
                print("   ❌ 注册也失败")
                return
        else:
            result = response.json()
            token = result['token']
            print("   ✅ 登录成功")
            
        print(f"   🔑 Token: {token[:20]}...")
        
    except Exception as e:
        print(f"   ❌ 认证失败: {e}")
        return
    
    # 2. 创建一个简单的PDF测试文件
    print("\n2️⃣ 创建测试PDF文件...")
    test_pdf_content = """This is a test PDF file for translation.
    
This document contains multiple paragraphs to test the PDF processing functionality.

The first paragraph talks about the importance of testing software before deployment.

The second paragraph discusses the benefits of automated testing in software development.

The third paragraph explains how proper testing can prevent bugs and improve user experience.

This is the final paragraph of our test document."""
    
    # 使用reportlab创建PDF
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        test_pdf_path = "test_translation.pdf"
        c = canvas.Canvas(test_pdf_path, pagesize=letter)
        
        # 添加文本到PDF
        y_position = 750
        for line in test_pdf_content.split('\n'):
            if line.strip():
                c.drawString(50, y_position, line.strip())
                y_position -= 20
        
        c.save()
        print(f"   ✅ 创建测试PDF: {test_pdf_path}")
        
    except Exception as e:
        print(f"   ❌ 创建PDF失败: {e}")
        return
    
    # 3. 上传PDF文件
    print("\n3️⃣ 上传PDF文件...")
    try:
        with open(test_pdf_path, 'rb') as f:
            files = {'file': (test_pdf_path, f, 'application/pdf')}
            upload_response = requests.post(f"{BASE_URL}/api/upload", files=files)
        
        print(f"   状态码: {upload_response.status_code}")
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            upload_id = upload_result['upload_id']
            print("   ✅ 文件上传成功")
            print(f"   📁 Upload ID: {upload_id}")
        else:
            print(f"   ❌ 上传失败: {upload_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return
    
    # 4. 开始翻译（使用较短的超时时间）
    print("\n4️⃣ 开始翻译...")
    translate_data = {
        "upload_id": upload_id,
        "target_language": "中文",
        "style": "casual"
    }
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        print("   发送翻译请求...")
        translate_response = requests.post(
            f"{BASE_URL}/api/translate",
            json=translate_data,
            headers=headers,
            timeout=60  # 60秒超时
        )
        
        print(f"   状态码: {translate_response.status_code}")
        if translate_response.status_code == 200:
            translate_result = translate_response.json()
            print("   ✅ 翻译请求成功")
            print(f"   📋 任务ID: {translate_result.get('job_id', 'N/A')}")
            print(f"   📊 状态: {translate_result.get('status', 'N/A')}")
        else:
            print(f"   ❌ 翻译失败: {translate_response.text}")
            
    except requests.exceptions.Timeout:
        print("   ⏰ 翻译请求超时")
    except Exception as e:
        print(f"   ❌ 翻译异常: {e}")
    
    # 清理测试文件
    try:
        if os.path.exists(test_pdf_path):
            os.remove(test_pdf_path)
            print(f"\n🧹 清理测试文件: {test_pdf_path}")
    except:
        pass
    
    print("\n✅ PDF翻译测试完成！")

if __name__ == "__main__":
    test_pdf_translation()
