#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务器稳定性
"""

import requests
import time

def test_server_endpoints():
    """测试服务器端点"""
    base_url = "http://127.0.0.1:5000"
    
    print("🚀 测试服务器稳定性...")
    
    # 测试主页
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        print(f"✅ 主页访问: {response.status_code}")
    except Exception as e:
        print(f"❌ 主页访问失败: {str(e)}")
        return False
    
    # 测试API端点
    try:
        # 测试上传端点（不实际上传文件）
        response = requests.options(f"{base_url}/api/upload", timeout=10)
        print(f"✅ 上传端点检查: {response.status_code}")
    except Exception as e:
        print(f"❌ 上传端点检查失败: {str(e)}")
    
    print("✅ 服务器稳定性测试通过")
    return True

def test_pdf_generation():
    """测试PDF生成功能"""
    print("\n📋 测试PDF生成功能...")
    
    try:
        from src.generator.multi_format_generator import MultiFormatGenerator
        
        generator = MultiFormatGenerator()
        
        # 简单测试数据
        original = ["Test"]
        translated = ["测试"]
        
        # 测试PDF生成
        success = generator._generate_pdf_format(
            original, translated, "test_stability.pdf"
        )
        
        print(f"PDF生成测试: {'✅ 成功' if success else '❌ 失败'}")
        
        # 清理
        generator.cleanup()
        
        return success
        
    except Exception as e:
        print(f"❌ PDF生成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 服务器稳定性和功能测试")
    print("=" * 50)
    
    # 测试服务器端点
    server_ok = test_server_endpoints()
    
    # 测试PDF生成
    pdf_ok = test_pdf_generation()
    
    print("\n📊 测试结果:")
    print(f"   服务器稳定性: {'✅ 通过' if server_ok else '❌ 失败'}")
    print(f"   PDF生成功能: {'✅ 通过' if pdf_ok else '❌ 失败'}")
    
    if server_ok and pdf_ok:
        print("\n🎉 所有测试通过！服务器可以正常使用")
    else:
        print("\n⚠️  部分测试失败，请检查问题")

if __name__ == "__main__":
    main()
