#!/usr/bin/env python3
"""
测试Web界面的用户认证功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_web_auth():
    """测试Web界面认证功能"""
    print("🌐 测试Web界面用户认证功能")
    print("=" * 50)
    
    # 1. 测试主页访问
    print("1️⃣ 测试主页访问...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 主页访问成功")
            # 检查是否包含认证相关元素
            content = response.text
            if 'auth-modal' in content and 'loginForm' in content:
                print("   ✅ 包含认证界面元素")
            else:
                print("   ❌ 缺少认证界面元素")
        else:
            print("   ❌ 主页访问失败")
    except Exception as e:
        print(f"   ❌ 主页访问异常: {e}")
    
    # 2. 测试注册API
    print("\n2️⃣ 测试注册API...")
    test_email = f"webtest_{int(time.time())}@example.com"
    register_data = {
        "email": test_email,
        "password": "Test123456"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register",
            json=register_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 201:
            result = response.json()
            print("   ✅ 注册成功")
            print(f"   📧 邮箱: {result['user']['email']}")
            print(f"   💰 初始积分: {result['user']['points_balance']}")
            token = result['token']
            print(f"   🔑 获得令牌: {token[:20]}...")
            
            # 3. 测试获取用户信息API
            print("\n3️⃣ 测试获取用户信息...")
            headers = {'Authorization': f'Bearer {token}'}
            me_response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
            print(f"   状态码: {me_response.status_code}")
            if me_response.status_code == 200:
                user_info = me_response.json()
                print("   ✅ 获取用户信息成功")
                print(f"   📧 邮箱: {user_info['user']['email']}")
                print(f"   💰 积分: {user_info['user']['points_balance']}")
            else:
                print("   ❌ 获取用户信息失败")
            
            # 4. 测试每日签到API
            print("\n4️⃣ 测试每日签到...")
            signin_response = requests.post(f"{BASE_URL}/api/auth/signin", headers=headers)
            print(f"   状态码: {signin_response.status_code}")
            if signin_response.status_code == 200:
                signin_result = signin_response.json()
                print("   ✅ 签到成功")
                print(f"   💰 签到后积分: {signin_result['points_balance']}")
            else:
                print("   ❌ 签到失败")
                
        else:
            error = response.json()
            print(f"   ❌ 注册失败: {error.get('error', '未知错误')}")
    except Exception as e:
        print(f"   ❌ 注册异常: {e}")
    
    # 5. 测试登录API
    print("\n5️⃣ 测试登录API...")
    login_data = {
        "email": test_email,
        "password": "Test123456"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 登录成功")
            print(f"   📧 邮箱: {result['user']['email']}")
            print(f"   💰 当前积分: {result['user']['points_balance']}")
        else:
            error = response.json()
            print(f"   ❌ 登录失败: {error.get('error', '未知错误')}")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    print("\n✅ Web界面认证功能测试完成！")

if __name__ == "__main__":
    test_web_auth()
