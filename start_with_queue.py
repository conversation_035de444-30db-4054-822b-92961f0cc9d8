#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本 - 包含后台任务队列
"""

import os
import sys
import logging
import signal
import time
from threading import Thread

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from src.utils.task_queue import start_task_queue, stop_task_queue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info("接收到停止信号，正在关闭服务...")
    stop_task_queue()
    sys.exit(0)

def main():
    """主函数"""
    logger.info("🚀 启动双语书籍翻译服务...")
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动后台任务队列
        logger.info("📋 启动后台任务队列...")
        start_task_queue()
        
        # 等待一下确保任务队列启动完成
        time.sleep(1)
        
        # 启动Flask应用
        logger.info("🌐 启动Web服务器...")
        logger.info("📱 访问地址: http://localhost:5000")
        logger.info("🔧 调试模式: 开启")
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # 禁用重载器以避免任务队列重复启动
        )
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在关闭服务...")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
    finally:
        # 停止任务队列
        logger.info("🛑 停止后台任务队列...")
        stop_task_queue()
        logger.info("✅ 服务已关闭")

if __name__ == '__main__':
    main()
