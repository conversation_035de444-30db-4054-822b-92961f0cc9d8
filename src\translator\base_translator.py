#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础翻译器类
"""

from abc import ABC, abstractmethod
import time
import logging

logger = logging.getLogger(__name__)


class BaseTranslator(ABC):
    """基础翻译器抽象类"""
    
    def __init__(self, api_key, target_language, style='casual'):
        self.api_key = api_key
        self.target_language = target_language
        self.style = style
        self.rate_limit_delay = 1  # 默认请求间隔1秒
        
    @abstractmethod
    def translate(self, text):
        """翻译文本的抽象方法"""
        pass
    
    def _apply_rate_limit(self):
        """应用速率限制"""
        if self.rate_limit_delay > 0:
            time.sleep(self.rate_limit_delay)
    
    def _is_special_text(self, text):
        """判断是否为特殊文本（不需要翻译）"""
        if not text or not text.strip():
            return True
        
        # 纯数字
        if text.strip().isdigit():
            return True
        
        # 纯空白字符
        if text.isspace():
            return True
        
        # 纯标点符号
        import string
        if all(char in string.punctuation + ' \t\n' for char in text):
            return True
        
        # 太短的文本
        if len(text.strip()) < 3:
            return True
        
        return False
    
    def get_prompt_template(self):
        """获取提示词模板"""
        if self.style == 'casual':
            return """你是一位友好的译者，请将以下内容翻译成{language}，用轻松口语的笔调，确保意思准确但可以自然润色。

原文：{text}

请只返回翻译结果，不要包含任何解释或其他内容。"""
        
        elif self.style == 'faithful':
            return """你是一位严谨的学术译者，请将以下内容翻译成{language}，要求逐句对齐，尽量保留原句结构与术语。

原文：{text}

请只返回翻译结果，不要包含任何解释或其他内容。"""
        
        else:
            return """请将以下内容准确翻译成{language}：

原文：{text}

请只返回翻译结果，不要包含任何解释或其他内容。"""
