#!/usr/bin/env python3
"""
带认证的翻译功能测试
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:5000"

def test_auth_translation():
    """测试带认证的翻译功能"""
    print("🔐 带认证的翻译功能测试")
    print("=" * 50)
    
    # 1. 注册新用户
    print("1️⃣ 注册新用户...")
    test_email = f"test_{int(time.time())}@example.com"
    register_data = {
        "email": test_email,
        "password": "Test123456"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register",
            json=register_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 201:
            result = response.json()
            token = result['token']
            user_info = result['user']
            print("   ✅ 注册成功")
            print(f"   📧 邮箱: {user_info['email']}")
            print(f"   💰 积分: {user_info['points_balance']}")
            print(f"   🔑 Token: {token[:20]}...")
        else:
            print(f"   ❌ 注册失败: {response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 注册异常: {e}")
        return
    
    # 2. 创建测试文件
    print("\n2️⃣ 创建测试文件...")
    test_content = """Hello, this is a test document for translation.

This document contains multiple paragraphs to test the translation functionality.

The first paragraph talks about the importance of testing software before deployment.

The second paragraph discusses the benefits of automated testing in software development.

This is the final paragraph of our test document."""
    
    test_file_path = "auth_test.txt"
    try:
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"   ✅ 创建测试文件: {test_file_path}")
    except Exception as e:
        print(f"   ❌ 创建文件失败: {e}")
        return
    
    # 3. 上传文件
    print("\n3️⃣ 上传文件...")
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path, f, 'text/plain')}
            upload_response = requests.post(f"{BASE_URL}/api/upload", files=files)
        
        print(f"   状态码: {upload_response.status_code}")
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            upload_id = upload_result['upload_id']
            print("   ✅ 文件上传成功")
            print(f"   📁 Upload ID: {upload_id}")
        else:
            print(f"   ❌ 上传失败: {upload_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return
    
    # 4. 带认证的翻译请求
    print("\n4️⃣ 发送翻译请求...")
    translate_data = {
        "upload_id": upload_id,
        "target_language": "中文",
        "style": "casual"
    }
    
    try:
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {token}'
        }
        
        print("   发送翻译请求...")
        translate_response = requests.post(
            f"{BASE_URL}/api/translate",
            json=translate_data,
            headers=headers,
            timeout=60  # 60秒超时
        )
        
        print(f"   状态码: {translate_response.status_code}")
        if translate_response.status_code == 200:
            translate_result = translate_response.json()
            print("   ✅ 翻译请求成功")
            print(f"   📋 任务ID: {translate_result.get('job_id', 'N/A')}")
            print(f"   📊 状态: {translate_result.get('status', 'N/A')}")
            print(f"   💬 消息: {translate_result.get('message', 'N/A')}")
        else:
            print(f"   ❌ 翻译失败")
            print(f"   📄 响应: {translate_response.text}")
            
    except requests.exceptions.Timeout:
        print("   ⏰ 翻译请求超时")
    except Exception as e:
        print(f"   ❌ 翻译异常: {e}")
    
    # 5. 检查用户积分
    print("\n5️⃣ 检查用户积分...")
    try:
        headers = {'Authorization': f'Bearer {token}'}
        me_response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        
        if me_response.status_code == 200:
            user_data = me_response.json()
            print(f"   💰 当前积分: {user_data['user']['points_balance']}")
        else:
            print("   ❌ 获取用户信息失败")
    except Exception as e:
        print(f"   ❌ 获取用户信息异常: {e}")
    
    # 清理测试文件
    try:
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"\n🧹 清理测试文件: {test_file_path}")
    except:
        pass
    
    print("\n✅ 带认证翻译测试完成！")

if __name__ == "__main__":
    test_auth_translation()
