#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理工具
"""

import os
import re


def allowed_file(filename, allowed_extensions):
    """检查文件是否为允许的类型"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions


def get_file_extension(filename):
    """获取文件扩展名"""
    return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''


def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    size_bytes = os.path.getsize(file_path)
    return size_bytes / (1024 * 1024)


def count_words(text):
    """统计文本词数（简单实现）"""
    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 分割单词
    words = re.findall(r'\b\w+\b', text)
    return len(words)


def sanitize_filename(filename):
    """清理文件名，移除不安全字符"""
    # 移除或替换不安全字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    return filename


def ensure_directory(directory):
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)


def cleanup_temp_files(directory, max_age_hours=24):
    """清理临时文件"""
    import time
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path):
            file_age = current_time - os.path.getmtime(file_path)
            if file_age > max_age_seconds:
                try:
                    os.remove(file_path)
                    print(f"已删除过期文件: {filename}")
                except Exception as e:
                    print(f"删除文件失败 {filename}: {str(e)}")
