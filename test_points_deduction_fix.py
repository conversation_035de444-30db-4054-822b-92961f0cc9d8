#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试积分扣除修复
验证只有翻译成功后才扣除积分
"""

import os
import sys
import uuid
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.user import User
from src.models.translation_job import TranslationJob
from src.utils.task_queue import TaskQueue
from src.models.points_transaction import PointsTransaction

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_points_deduction_on_success():
    """测试翻译成功后扣除积分"""
    print("\n💰 测试翻译成功后扣除积分")
    print("=" * 50)
    
    try:
        # 1. 创建测试用户
        test_email = "<EMAIL>"
        test_user = User.create_user(test_email, "password123")
        initial_points = test_user.points_balance
        logger.info(f"创建测试用户: {test_email}, 初始积分: {initial_points}")
        
        # 2. 创建翻译任务（不扣除积分）
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test_sample.txt",
            file_path="test_sample.txt",
            target_language="English",
            style="casual",
            status="pending"
        )
        job.user_id = test_user.user_id
        job.cost_points = 50
        job.save()
        logger.info(f"创建翻译任务: {job_id}")
        
        # 3. 验证任务创建后积分未扣除
        test_user = User.load(test_user.user_id)  # 重新加载
        if test_user.points_balance == initial_points:
            logger.info("✅ 任务创建后积分未扣除（正确）")
        else:
            logger.error("❌ 任务创建后积分被扣除（错误）")
            return False
        
        # 4. 模拟翻译成功，测试积分扣除
        queue = TaskQueue()
        
        # 模拟翻译成功的积分扣除逻辑
        from src.utils.auth_utils import deduct_points_for_translation
        success, deducted_points, error_msg = deduct_points_for_translation(test_user, job_id)
        
        if success:
            logger.info(f"✅ 翻译成功后积分扣除成功: {deducted_points}")
            
            # 验证积分确实被扣除
            test_user = User.load(test_user.user_id)  # 重新加载
            expected_points = initial_points - deducted_points
            if test_user.points_balance == expected_points:
                logger.info(f"✅ 积分余额正确: {test_user.points_balance}")
                result = True
            else:
                logger.error(f"❌ 积分余额错误: 期望{expected_points}, 实际{test_user.points_balance}")
                result = False
        else:
            logger.error(f"❌ 积分扣除失败: {error_msg}")
            result = False
        
        # 5. 验证积分交易记录
        transactions = PointsTransaction.get_user_transactions(test_user.user_id)
        translation_transactions = [t for t in transactions if t.transaction_type == 'translation']
        
        if len(translation_transactions) > 0:
            logger.info(f"✅ 找到翻译交易记录: {len(translation_transactions)} 条")
            for t in translation_transactions:
                logger.info(f"  - 交易: {t.description}, 金额: {t.amount}, 关联任务: {t.related_id}")
        else:
            logger.warning("⚠️ 未找到翻译交易记录")
        
        # 6. 清理测试数据
        try:
            os.remove(f'data/users/{test_user.user_id}.json')
            os.remove(f'data/jobs/{job_id}.json')
            for t in transactions:
                try:
                    os.remove(f'data/transactions/{t.transaction_id}.json')
                except:
                    pass
        except Exception as e:
            logger.warning(f"清理测试数据时出现警告: {str(e)}")
        
        print("✅ 积分扣除逻辑测试通过" if result else "❌ 积分扣除逻辑测试失败")
        return result
        
    except Exception as e:
        logger.error(f"积分扣除逻辑测试失败: {str(e)}")
        print("❌ 积分扣除逻辑测试失败")
        return False

def test_points_not_deducted_on_failure():
    """测试翻译失败时不扣除积分"""
    print("\n💸 测试翻译失败时不扣除积分")
    print("=" * 50)
    
    try:
        # 1. 创建测试用户
        test_email = "<EMAIL>"
        test_user = User.create_user(test_email, "password123")
        initial_points = test_user.points_balance
        logger.info(f"创建测试用户: {test_email}, 初始积分: {initial_points}")
        
        # 2. 创建翻译任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test_sample.txt",
            file_path="nonexistent_file.txt",  # 不存在的文件，会导致失败
            target_language="English",
            style="casual",
            status="pending"
        )
        job.user_id = test_user.user_id
        job.cost_points = 50
        job.save()
        logger.info(f"创建翻译任务: {job_id}")
        
        # 3. 模拟翻译失败
        job.status = 'failed'
        job.error_message = "文件不存在"
        job.save()
        logger.info("模拟翻译失败")
        
        # 4. 验证积分未被扣除
        test_user = User.load(test_user.user_id)  # 重新加载
        if test_user.points_balance == initial_points:
            logger.info("✅ 翻译失败时积分未扣除（正确）")
            result = True
        else:
            logger.error(f"❌ 翻译失败但积分被扣除（错误）: {test_user.points_balance}")
            result = False
        
        # 5. 验证没有翻译相关的积分交易记录
        transactions = PointsTransaction.get_user_transactions(test_user.user_id)
        translation_transactions = [t for t in transactions if t.transaction_type == 'translation']
        
        if len(translation_transactions) == 0:
            logger.info("✅ 翻译失败时没有产生翻译交易记录（正确）")
        else:
            logger.warning(f"⚠️ 翻译失败但产生了交易记录: {len(translation_transactions)} 条")
        
        # 6. 清理测试数据
        try:
            os.remove(f'data/users/{test_user.user_id}.json')
            os.remove(f'data/jobs/{job_id}.json')
            for t in transactions:
                try:
                    os.remove(f'data/transactions/{t.transaction_id}.json')
                except:
                    pass
        except Exception as e:
            logger.warning(f"清理测试数据时出现警告: {str(e)}")
        
        print("✅ 翻译失败积分保护测试通过" if result else "❌ 翻译失败积分保护测试失败")
        return result
        
    except Exception as e:
        logger.error(f"翻译失败积分保护测试失败: {str(e)}")
        print("❌ 翻译失败积分保护测试失败")
        return False

def main():
    """主测试函数"""
    print("💰 积分扣除修复验证")
    print("=" * 60)
    
    # 确保目录存在
    required_dirs = ['data/users', 'data/jobs', 'data/transactions']
    for dir_path in required_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # 运行测试
    tests = [
        ("翻译成功后扣除积分", test_points_deduction_on_success),
        ("翻译失败时不扣除积分", test_points_not_deducted_on_failure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！积分扣除逻辑已修复")
        print("💡 现在只有翻译成功后才会扣除积分")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
