#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TXT文件加载器
"""

import os
import logging
from tqdm import tqdm
from .base_loader import BaseLoader

logger = logging.getLogger(__name__)


class TXTLoader(BaseLoader):
    """TXT文件加载器"""
    
    def __init__(self, file_path, translator):
        super().__init__(file_path, translator)
        self.batch_size = 10  # 批量处理段落数量
    
    def make_bilingual_book(self, output_path):
        """生成双语版本的TXT文件"""
        logger.info(f"开始处理TXT文件: {self.file_path}")
        
        try:
            # 读取原文件
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按段落分割
            paragraphs = self._split_into_paragraphs(content)
            logger.info(f"共找到 {len(paragraphs)} 个段落")
            
            # 翻译段落
            bilingual_content = []
            
            with tqdm(total=len(paragraphs), desc="翻译进度") as pbar:
                for i, paragraph in enumerate(paragraphs):
                    if self._should_translate(paragraph):
                        try:
                            translated = self.translator.translate(paragraph)
                            bilingual_paragraph = self._create_bilingual_paragraph(paragraph, translated)
                            bilingual_content.append(bilingual_paragraph)
                            logger.debug(f"段落 {i+1} 翻译完成")
                        except Exception as e:
                            logger.error(f"段落 {i+1} 翻译失败: {str(e)}")
                            # 翻译失败时保留原文
                            bilingual_content.append(paragraph)
                    else:
                        # 不需要翻译的段落直接保留
                        bilingual_content.append(paragraph)
                    
                    pbar.update(1)
            
            # 写入输出文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(bilingual_content))
            
            logger.info(f"双语TXT文件生成完成: {output_path}")
            
        except Exception as e:
            logger.error(f"处理TXT文件失败: {str(e)}")
            raise
    
    def _split_into_paragraphs(self, content):
        """将内容分割为段落"""
        # 按双换行符分割段落
        paragraphs = content.split('\n\n')
        
        # 过滤空段落
        paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        # 如果没有双换行符，按单换行符分割
        if len(paragraphs) == 1:
            paragraphs = content.split('\n')
            paragraphs = [p.strip() for p in paragraphs if p.strip()]
        
        return paragraphs
    
    def _create_bilingual_paragraph(self, original, translated):
        """创建双语段落"""
        return f"{original}\n\n{translated}"
