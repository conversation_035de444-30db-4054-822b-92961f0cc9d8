# 雙語書籍翻譯服務 - MVP版本

## 项目简介

这是一个基于Gemini API的双语书籍翻译服务MVP版本，可以将TXT和EPUB格式的书籍翻译成双语版本。

## 功能特性

- ✅ 支持TXT和EPUB文件格式
- ✅ 基于Gemini API的高质量翻译
- ✅ 多种翻译风格（口语化/严谨忠实）
- ✅ 双语版本文件生成
- ✅ 简洁的Web界面
- ✅ 文件上传和下载
- ✅ 翻译进度跟踪

## 技术栈

- **后端**: Python Flask
- **翻译**: Google Gemini API
- **文件处理**: ebooklib, BeautifulSoup
- **前端**: HTML/CSS/JavaScript
- **存储**: 本地文件系统

## 安装和运行

### 1. 环境要求

- Python 3.8+
- pip

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的Gemini API密钥：
```
GEMINI_API_KEY=your_gemini_api_key_here
SECRET_KEY=your_secret_key_here
```

### 4. 运行应用

```bash
python run.py
```

应用将在 `http://localhost:5000` 启动。

## 使用方法

1. 打开浏览器访问 `http://localhost:5000`
2. 上传TXT或EPUB文件（最大100MB）
3. 选择目标语言和翻译风格
4. 点击"开始翻译"
5. 等待翻译完成后下载双语版本文件

## API接口

### 文件上传
```
POST /api/upload
Content-Type: multipart/form-data
```

### 开始翻译
```
POST /api/translate
Content-Type: application/json
{
  "upload_id": "uuid",
  "target_language": "中文",
  "style": "casual"
}
```

### 查询任务状态
```
GET /api/job/{job_id}
```

### 下载结果
```
GET /api/download/{job_id}
```

## 项目结构

```
bilingual_read/
├── app.py                 # 主应用程序
├── run.py                 # 启动脚本
├── requirements.txt       # 依赖包
├── .env.example          # 环境变量模板
├── src/                  # 源代码
│   ├── translator/       # 翻译器模块
│   ├── loader/          # 文件加载器
│   ├── models/          # 数据模型
│   └── utils/           # 工具函数
├── templates/           # HTML模板
├── uploads/            # 上传文件目录
├── outputs/            # 输出文件目录
└── data/               # 数据存储目录
```

## 注意事项

1. 确保有稳定的网络连接以访问Gemini API
2. 大文件翻译可能需要较长时间，请耐心等待
3. 翻译质量取决于原文质量和Gemini API的表现
4. 建议在翻译前备份原文件

## 后续开发计划

- [ ] 用户认证和积分系统
- [ ] 支持更多文件格式（PDF、DOCX）
- [ ] 批量翻译功能
- [ ] 翻译历史记录
- [ ] 云存储集成
- [ ] 移动端适配

## 故障排除

### 常见问题

1. **翻译失败**: 检查Gemini API密钥是否正确配置
2. **文件上传失败**: 确认文件格式和大小符合要求
3. **页面无法访问**: 检查端口5000是否被占用

### 日志查看

应用运行时会在控制台输出详细日志，可以根据日志信息排查问题。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
