#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础文件加载器类
"""

from abc import ABC, abstractmethod
import os
import logging

logger = logging.getLogger(__name__)


class BaseLoader(ABC):
    """基础文件加载器抽象类"""
    
    def __init__(self, file_path, translator):
        self.file_path = file_path
        self.translator = translator
        self.translation_style = "color: #808080; font-style: italic;"
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
    
    @abstractmethod
    def make_bilingual_book(self, output_path):
        """生成双语版本文件的抽象方法"""
        pass
    
    def _create_bilingual_content(self, original_text, translated_text):
        """创建双语内容"""
        return f"{original_text}\n\n{translated_text}"
    
    def _should_translate(self, text):
        """判断文本是否需要翻译"""
        if not text or not text.strip():
            return False
        
        # 过滤掉太短的文本
        if len(text.strip()) < 3:
            return False
        
        # 过滤掉纯数字
        if text.strip().isdigit():
            return False
        
        # 过滤掉纯标点符号
        import string
        if all(char in string.punctuation + ' \t\n' for char in text):
            return False
        
        return True
