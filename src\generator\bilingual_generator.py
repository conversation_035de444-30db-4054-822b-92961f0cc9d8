#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双语文档生成器
"""

import os
import logging
from typing import List
from datetime import datetime

logger = logging.getLogger(__name__)


class BilingualGenerator:
    """双语文档生成器"""
    
    def __init__(self):
        pass
    
    def generate_bilingual_txt(self, original_chapters: List[str], 
                              translated_chapters: List[str], 
                              output_path: str):
        """生成双语TXT文件"""
        if len(original_chapters) != len(translated_chapters):
            raise ValueError("原文和译文章节数量不匹配")
        
        bilingual_content = []
        
        for i, (original, translated) in enumerate(zip(original_chapters, translated_chapters)):
            # 创建双语段落
            bilingual_paragraph = f"{original}\n\n{translated}"
            bilingual_content.append(bilingual_paragraph)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n\n---\n\n'.join(bilingual_content))
        
        logger.info(f"双语TXT文件生成完成: {output_path}")
    
    def generate_bilingual_epub(self, original_chapters: List[str], 
                               translated_chapters: List[str], 
                               output_path: str):
        """生成双语EPUB文件"""
        try:
            from ebooklib import epub
            
            if len(original_chapters) != len(translated_chapters):
                raise ValueError("原文和译文章节数量不匹配")
            
            # 创建EPUB书籍
            book = epub.EpubBook()
            
            # 设置元数据
            book.set_identifier('bilingual_book_' + str(int(datetime.now().timestamp())))
            book.set_title('双语版本')
            book.set_language('zh')
            book.add_author('翻译系统')
            
            # 创建章节
            chapters = []
            spine = ['nav']
            
            # 将所有内容合并到一个章节中，而不是分成多个章节
            all_content = []
            for i, (original, translated) in enumerate(zip(original_chapters, translated_chapters)):
                all_content.append(f'<div class="original">{self._text_to_html(original)}</div>')
                all_content.append(f'<div class="translated">{self._text_to_html(translated)}</div>')
                # 段落间添加分隔
                if i < len(original_chapters) - 1:
                    all_content.append('<div class="separator"><br/></div>')

            # 创建单一章节内容
            chapter_content = f"""
            <html>
            <head>
                <title>双语文档</title>
                <style>
                    .original {{ margin-bottom: 10px; line-height: 1.6; }}
                    .translated {{ color: #666; font-style: italic; margin-bottom: 20px; line-height: 1.6; }}
                    .separator {{ margin: 10px 0; }}
                </style>
            </head>
            <body>
                {''.join(all_content)}
            </body>
            </html>
            """

            # 创建单一EPUB章节
            chapter = epub.EpubHtml(
                title='双语文档',
                file_name='bilingual_content.xhtml',
                lang='zh'
            )
            chapter.content = chapter_content

            book.add_item(chapter)
            chapters.append(chapter)
            spine.append(chapter)
            
            # 添加导航
            book.toc = chapters
            book.add_item(epub.EpubNcx())
            book.add_item(epub.EpubNav())
            
            # 设置spine
            book.spine = spine
            
            # 写入EPUB文件
            epub.write_epub(output_path, book, {})
            
            logger.info(f"双语EPUB文件生成完成: {output_path}")
            
        except ImportError:
            logger.error("需要安装ebooklib: pip install ebooklib")
            raise
        except Exception as e:
            logger.error(f"生成EPUB文件失败: {str(e)}")
            raise
    
    def _text_to_html(self, text: str) -> str:
        """将文本转换为HTML"""
        # 简单的文本到HTML转换
        html_text = text.replace('\n', '<br/>')
        html_text = html_text.replace('&', '&amp;')
        html_text = html_text.replace('<', '&lt;')
        html_text = html_text.replace('>', '&gt;')
        return html_text
