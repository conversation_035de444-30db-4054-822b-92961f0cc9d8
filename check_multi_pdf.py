#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查多段落PDF文件内容
"""

import PyPDF2
import os

def check_multi_pdf():
    """检查多段落PDF文件内容"""
    pdf_path = "test_multi_paragraph_output/multi_paragraph.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"文件不存在: {pdf_path}")
        return
    
    try:
        with open(pdf_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            print(f"PDF文件: {pdf_path}")
            print(f"页数: {len(reader.pages)}")
            
            if reader.pages:
                text = reader.pages[0].extract_text()
                print(f"第一页文本长度: {len(text)} 字符")
                print("第一页内容:")
                print("-" * 60)
                print(text)
                print("-" * 60)
                
                # 检查中文字符
                chinese_chars = [c for c in text if ord(c) > 127]
                print(f"中文字符数量: {len(chinese_chars)}")
                
                # 检查方块字符
                black_squares = text.count('■')
                print(f"方块字符(■)数量: {black_squares}")
                
                # 检查不需要的标题
                unwanted_titles = ["双语文档", "第一章", "第二章", "第三章", "第四章", "第五章"]
                found_titles = [title for title in unwanted_titles if title in text]
                
                if found_titles:
                    print(f"⚠️  发现不需要的标题: {found_titles}")
                else:
                    print("✅ 无不需要的标题")
                
                if black_squares > 0:
                    print("⚠️  发现方块字符，中文字体可能有问题")
                else:
                    print("✅ 没有发现方块字符，中文显示正常")
                    
    except Exception as e:
        print(f"读取PDF失败: {str(e)}")

if __name__ == "__main__":
    check_multi_pdf()
