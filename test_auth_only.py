#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仅测试用户认证功能（不包含翻译）
"""

import requests
import json

def test_complete_auth_flow():
    """测试完整的认证流程"""
    print("🚀 完整用户认证流程测试")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # 测试1: 用户注册
    print("\n1️⃣ 测试用户注册...")
    register_data = {
        "email": "<EMAIL>",
        "password": "secure123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/register", json=register_data, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ 注册成功: {result['message']}")
            token = result['token']
            user_info = result['user']
            print(f"   📧 邮箱: {user_info['email']}")
            print(f"   💰 初始积分: {user_info['points_balance']}")
        elif response.status_code == 400 and "已被注册" in response.json().get('error', ''):
            print("   ℹ️ 用户已存在，跳过注册")
            token = None
        else:
            print(f"   ❌ 注册失败: {response.json()}")
            token = None
    except Exception as e:
        print(f"   ❌ 注册请求失败: {str(e)}")
        token = None
    
    # 测试2: 用户登录
    print("\n2️⃣ 测试用户登录...")
    login_data = {
        "email": "<EMAIL>",
        "password": "secure123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 登录成功: {result['message']}")
            token = result['token']
            user_info = result['user']
            print(f"   📧 邮箱: {user_info['email']}")
            print(f"   💰 当前积分: {user_info['points_balance']}")
        else:
            print(f"   ❌ 登录失败: {response.json()}")
            return
    except Exception as e:
        print(f"   ❌ 登录请求失败: {str(e)}")
        return
    
    # 测试3: 获取用户信息
    print("\n3️⃣ 测试获取用户信息...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            user_info = result['user']
            print(f"   ✅ 获取成功:")
            print(f"   📧 邮箱: {user_info['email']}")
            print(f"   💰 积分余额: {user_info['points_balance']}")
            print(f"   📅 注册时间: {user_info['created_at']}")
            print(f"   ✅ 账户状态: {'活跃' if user_info['is_active'] else '禁用'}")
        else:
            print(f"   ❌ 获取失败: {response.json()}")
    except Exception as e:
        print(f"   ❌ 获取用户信息请求失败: {str(e)}")
    
    # 测试4: 每日签到
    print("\n4️⃣ 测试每日签到...")
    
    try:
        response = requests.post(f"{base_url}/api/auth/signin", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 签到成功: {result['message']}")
            print(f"   💰 签到后积分: {result['points_balance']}")
        elif response.status_code == 400:
            result = response.json()
            print(f"   ℹ️ 签到信息: {result['message']}")
            print(f"   💰 当前积分: {result['points_balance']}")
        else:
            print(f"   ❌ 签到失败: {response.json()}")
    except Exception as e:
        print(f"   ❌ 签到请求失败: {str(e)}")
    
    # 测试5: 无效令牌测试
    print("\n5️⃣ 测试无效令牌...")
    invalid_headers = {"Authorization": "Bearer invalid_token"}
    
    try:
        response = requests.get(f"{base_url}/api/auth/me", headers=invalid_headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 401:
            print(f"   ✅ 正确拒绝无效令牌: {response.json()['error']}")
        else:
            print(f"   ❌ 应该拒绝无效令牌，但状态码是: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 无效令牌测试失败: {str(e)}")
    
    # 测试6: 密码验证
    print("\n6️⃣ 测试密码验证...")
    weak_passwords = [
        ("123", "密码太短"),
        ("password", "只有字母"),
        ("123456", "只有数字")
    ]
    
    for weak_pwd, description in weak_passwords:
        test_data = {
            "email": f"test_{len(weak_pwd)}@test.com",
            "password": weak_pwd
        }
        
        try:
            response = requests.post(f"{base_url}/api/auth/register", json=test_data, timeout=5)
            if response.status_code == 400:
                print(f"   ✅ 正确拒绝{description}: {response.json()['error']}")
            else:
                print(f"   ❌ 应该拒绝{description}")
        except Exception as e:
            print(f"   ❌ 密码验证测试失败: {str(e)}")
    
    print("\n✅ 用户认证系统测试完成！")
    print("\n📊 测试结果总结:")
    print("✅ 用户注册功能正常")
    print("✅ 用户登录功能正常") 
    print("✅ JWT令牌验证正常")
    print("✅ 用户信息获取正常")
    print("✅ 每日签到功能正常")
    print("✅ 密码强度验证正常")
    print("✅ 安全性验证正常")

def test_points_calculation():
    """测试积分计算逻辑"""
    print("\n\n🧮 积分计算逻辑测试")
    print("=" * 30)
    
    # 测试积分计算函数
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from src.utils.auth_utils import deduct_points_for_translation, check_points_sufficient
        from src.models.user import User
        
        # 创建测试用户
        test_user = User("test_calc", "<EMAIL>", "hash", 100)
        
        # 测试1: 积分充足
        print("1️⃣ 测试积分充足情况...")
        sufficient, error = check_points_sufficient(test_user, 50)
        print(f"   检查50积分: {'✅ 充足' if sufficient else '❌ 不足'}")
        
        # 测试2: 积分不足
        print("2️⃣ 测试积分不足情况...")
        sufficient, error = check_points_sufficient(test_user, 150)
        print(f"   检查150积分: {'✅ 充足' if sufficient else '❌ 不足'}")
        print(f"   错误信息: {error}")
        
        # 测试3: 积分扣除
        print("3️⃣ 测试积分扣除...")
        word_count = 250  # 250词应该需要6积分
        success, points, error = deduct_points_for_translation(test_user, word_count)
        print(f"   250词翻译: {'✅ 成功' if success else '❌ 失败'}")
        print(f"   扣除积分: {points}")
        print(f"   剩余积分: {test_user.points_balance}")
        
        print("\n✅ 积分计算测试完成！")
        
    except Exception as e:
        print(f"❌ 积分计算测试失败: {str(e)}")

if __name__ == "__main__":
    # 测试认证流程
    test_complete_auth_flow()
    
    # 测试积分计算
    test_points_calculation()
