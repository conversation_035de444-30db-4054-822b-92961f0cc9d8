#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的用户认证测试
"""

import requests
import json
import time

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    try:
        response = requests.get("http://localhost:5000/")
        if response.status_code == 200:
            print("✅ 服务器连接成功")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {str(e)}")
        return False

def test_user_registration():
    """测试用户注册"""
    print("\n🔐 测试用户注册...")
    
    url = "http://localhost:5000/api/auth/register"
    data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✅ 注册成功: {result.get('message', '')}")
            return result.get('token')
        else:
            result = response.json()
            print(f"❌ 注册失败: {result.get('error', '未知错误')}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except Exception as e:
        print(f"❌ 注册请求失败: {str(e)}")
        return None

def test_user_login():
    """测试用户登录"""
    print("\n🔑 测试用户登录...")
    
    url = "http://localhost:5000/api/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功: {result.get('message', '')}")
            return result.get('token')
        else:
            result = response.json()
            print(f"❌ 登录失败: {result.get('error', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None

def test_get_user_info(token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    
    if not token:
        print("❌ 没有有效的令牌")
        return
    
    url = "http://localhost:5000/api/auth/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            user_info = result.get('user', {})
            print(f"✅ 用户信息获取成功:")
            print(f"   邮箱: {user_info.get('email')}")
            print(f"   积分余额: {user_info.get('points_balance')}")
            print(f"   注册时间: {user_info.get('created_at')}")
        else:
            result = response.json()
            print(f"❌ 获取用户信息失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 获取用户信息请求失败: {str(e)}")

def test_daily_signin(token):
    """测试每日签到"""
    print("\n📅 测试每日签到...")
    
    if not token:
        print("❌ 没有有效的令牌")
        return
    
    url = "http://localhost:5000/api/auth/signin"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 签到成功: {result.get('message', '')}")
            print(f"   当前积分: {result.get('points_balance')}")
        else:
            result = response.json()
            print(f"❌ 签到失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 签到请求失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 用户管理系统测试")
    print("=" * 50)
    
    # 测试服务器连接
    if not test_server_connection():
        print("\n❌ 服务器未启动，请先运行: python app.py")
        return
    
    # 等待一下确保服务器完全启动
    time.sleep(1)
    
    # 测试用户注册
    token = test_user_registration()
    
    # 如果注册失败（可能用户已存在），尝试登录
    if not token:
        print("\n💡 注册失败，尝试登录现有用户...")
        token = test_user_login()
    
    # 测试其他功能
    if token:
        test_get_user_info(token)
        test_daily_signin(token)
    else:
        print("\n❌ 无法获取有效令牌，跳过后续测试")
    
    print("\n✅ 测试完成！")
    print("\n📝 测试说明:")
    print("1. 如果看到错误，请检查服务器是否正常运行")
    print("2. 用户数据存储在 data/users/ 目录")
    print("3. 可以多次运行测试验证功能")

if __name__ == "__main__":
    main()
