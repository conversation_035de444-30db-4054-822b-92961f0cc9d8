#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多格式文件加载器
支持多种格式的文件加载，自动转换为EPUB格式进行处理
"""

import os
import tempfile
import logging
from typing import List, Optional, Tuple
from pathlib import Path
from .txt_loader import TXTLoader
from .epub_loader import EPUBLoader
from ..converter.format_converter import FormatConverter

logger = logging.getLogger(__name__)


class MultiFormatLoader:
    """多格式文件加载器"""
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化多格式加载器
        
        Args:
            temp_dir: 临时目录路径
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.converter = FormatConverter(temp_dir)
        self.txt_loader = TXTLoader
        self.epub_loader = EPUBLoader
        self.temp_files = []  # 跟踪临时文件
    
    def is_supported(self, file_path: str) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持该格式
        """
        return self.converter.is_supported_input(file_path)
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的输入格式列表"""
        return list(self.converter.SUPPORTED_INPUT_FORMATS)
    
    def load_file(self, file_path: str) -> Tuple[List[str], str]:
        """
        加载文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[List[str], str]: (章节列表, 原始格式)
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not self.is_supported(file_path):
            raise ValueError(f"不支持的文件格式: {file_path}")
        
        original_format = self.converter.get_file_format(file_path)
        logger.info(f"加载文件: {file_path} (格式: {original_format})")
        
        try:
            # 如果是TXT格式，直接读取内容
            if original_format == 'txt':
                chapters = self._load_txt_content(file_path)
                return chapters, original_format

            # 如果是EPUB格式，使用EPUB加载器
            elif original_format == 'epub':
                chapters = self._load_epub_content(file_path)
                return chapters, original_format

            # 如果是DOCX格式，尝试直接处理
            elif original_format == 'docx':
                chapters = self._load_docx_content(file_path)
                return chapters, original_format

            # 如果是PDF格式，尝试直接处理
            elif original_format == 'pdf':
                chapters = self._load_pdf_content(file_path)
                return chapters, original_format
            
            # 其他格式需要先转换为EPUB
            else:
                return self._load_with_conversion(file_path, original_format)
                
        except Exception as e:
            logger.error(f"加载文件失败: {str(e)}")
            self.cleanup()
            raise
    
    def _load_with_conversion(self, file_path: str, original_format: str) -> Tuple[List[str], str]:
        """
        通过格式转换加载文件
        
        Args:
            file_path: 原始文件路径
            original_format: 原始格式
            
        Returns:
            Tuple[List[str], str]: (章节列表, 原始格式)
        """
        logger.info(f"需要转换格式: {original_format} -> epub")
        
        # 生成临时EPUB文件路径
        base_name = Path(file_path).stem
        temp_epub_path = os.path.join(self.temp_dir, f"{base_name}_converted.epub")
        self.temp_files.append(temp_epub_path)
        
        # 转换为EPUB格式
        if not self.converter.convert_to_epub(file_path, temp_epub_path):
            raise RuntimeError(f"格式转换失败: {original_format} -> epub")
        
        logger.info(f"格式转换成功: {temp_epub_path}")
        
        # 使用EPUB加载器加载转换后的文件
        chapters = self._load_epub_content(temp_epub_path)

        return chapters, original_format

    def _load_txt_content(self, file_path: str) -> List[str]:
        """加载TXT文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 按段落分割
        paragraphs = content.split('\n\n')
        paragraphs = [p.strip() for p in paragraphs if p.strip()]

        # 如果没有双换行符，按单换行符分割
        if len(paragraphs) == 1:
            paragraphs = content.split('\n')
            paragraphs = [p.strip() for p in paragraphs if p.strip()]

        return paragraphs

    def _load_epub_content(self, file_path: str) -> List[str]:
        """加载EPUB文件内容"""
        try:
            from ebooklib import epub
            import html2text

            book = epub.read_epub(file_path)
            chapters = []

            for item in book.get_items():
                if item.get_type() == epub.EpubHtml:
                    content = item.get_content().decode('utf-8')
                    # 转换HTML为纯文本
                    h = html2text.HTML2Text()
                    h.ignore_links = True
                    h.ignore_images = True
                    text = h.handle(content)

                    # 按段落分割
                    paragraphs = text.split('\n\n')
                    paragraphs = [p.strip() for p in paragraphs if p.strip() and len(p.strip()) > 10]
                    chapters.extend(paragraphs)

            return chapters

        except ImportError:
            logger.error("需要安装ebooklib和html2text: pip install ebooklib html2text")
            raise
        except Exception as e:
            logger.error(f"加载EPUB文件失败: {str(e)}")
            raise

    def _load_docx_content(self, file_path: str) -> List[str]:
        """加载DOCX文件内容（不依赖Calibre）"""
        try:
            from docx import Document

            doc = Document(file_path)
            chapters = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text and len(text) > 10:  # 过滤太短的段落
                    chapters.append(text)

            # 如果段落太少，尝试按句子分割
            if len(chapters) < 3:
                all_text = ' '.join(chapters)
                sentences = all_text.split('. ')
                chapters = [s.strip() + '.' for s in sentences if s.strip() and len(s.strip()) > 20]

            logger.info(f"从DOCX文件提取了 {len(chapters)} 个段落")
            return chapters

        except ImportError:
            logger.error("需要安装python-docx: pip install python-docx")
            # 降级到Calibre转换
            raise RuntimeError("DOCX处理需要安装python-docx包或Calibre")
        except Exception as e:
            logger.error(f"加载DOCX文件失败: {str(e)}")
            raise

    def _load_pdf_content(self, file_path: str) -> List[str]:
        """加载PDF文件内容（不依赖Calibre）"""
        try:
            import pdfplumber

            chapters = []

            with pdfplumber.open(file_path) as pdf:
                all_text = ""
                for page in pdf.pages:
                    text = page.extract_text()
                    if text:
                        all_text += text + "\n"

                # 按段落分割
                paragraphs = [p.strip() for p in all_text.split('\n\n') if p.strip()]

                # 过滤太短的段落
                for paragraph in paragraphs:
                    if len(paragraph) > 50:  # 只保留较长的段落
                        chapters.append(paragraph)

                # 如果段落太少，尝试按句子分割
                if len(chapters) < 3:
                    sentences = []
                    for paragraph in paragraphs:
                        if len(paragraph) > 20:
                            # 按句号分割
                            sents = [s.strip() + '.' for s in paragraph.split('.') if s.strip() and len(s.strip()) > 30]
                            sentences.extend(sents)

                    if sentences:
                        chapters = sentences

                # 如果还是太少，按页面分割
                if len(chapters) < 2:
                    chapters = []
                    for i, page in enumerate(pdf.pages):
                        text = page.extract_text()
                        if text and len(text.strip()) > 100:
                            chapters.append(f"第{i+1}页内容：\n{text.strip()}")

            logger.info(f"从PDF文件提取了 {len(chapters)} 个段落")
            return chapters

        except ImportError:
            logger.error("需要安装pdfplumber: pip install pdfplumber")
            # 降级到Calibre转换
            raise RuntimeError("PDF处理需要安装pdfplumber包或Calibre")
        except Exception as e:
            logger.error(f"加载PDF文件失败: {str(e)}")
            # 尝试使用PyPDF2作为备用方案
            try:
                return self._load_pdf_with_pypdf2(file_path)
            except Exception as e2:
                logger.error(f"PyPDF2备用方案也失败: {str(e2)}")
                raise RuntimeError(f"PDF文件处理失败: {str(e)}")

    def _load_pdf_with_pypdf2(self, file_path: str) -> List[str]:
        """使用PyPDF2加载PDF文件的备用方法"""
        try:
            import PyPDF2

            chapters = []

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                all_text = ""
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        all_text += text + "\n"

                # 按段落分割
                paragraphs = [p.strip() for p in all_text.split('\n\n') if p.strip() and len(p.strip()) > 50]

                if paragraphs:
                    chapters = paragraphs
                else:
                    # 如果没有段落，按页面分割
                    for i, page in enumerate(pdf_reader.pages):
                        text = page.extract_text()
                        if text and len(text.strip()) > 50:
                            chapters.append(f"第{i+1}页：{text.strip()}")

            logger.info(f"使用PyPDF2从PDF文件提取了 {len(chapters)} 个段落")
            return chapters

        except Exception as e:
            logger.error(f"PyPDF2加载PDF失败: {str(e)}")
            raise
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_files:
            self.converter.cleanup_temp_files(self.temp_files)
            self.temp_files.clear()
            logger.debug("已清理临时文件")
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup()


class EnhancedFileLoader:
    """增强的文件加载器 - 向后兼容的接口"""
    
    def __init__(self):
        self.multi_loader = MultiFormatLoader()
    
    def load_file(self, file_path: str) -> List[str]:
        """
        加载文件内容（向后兼容接口）
        
        Args:
            file_path: 文件路径
            
        Returns:
            List[str]: 章节列表
        """
        chapters, _ = self.multi_loader.load_file(file_path)
        return chapters
    
    def is_supported(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        return self.multi_loader.is_supported(file_path)
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return self.multi_loader.get_supported_formats()
    
    def cleanup(self):
        """清理临时文件"""
        self.multi_loader.cleanup()


# 为了向后兼容，提供一个简单的工厂函数
def create_file_loader(file_path: str):
    """
    根据文件格式创建合适的加载器
    
    Args:
        file_path: 文件路径
        
    Returns:
        适合的文件加载器实例
    """
    loader = EnhancedFileLoader()
    
    if not loader.is_supported(file_path):
        raise ValueError(f"不支持的文件格式: {file_path}")
    
    return loader


def get_file_info(file_path: str) -> dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 文件信息
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    converter = FormatConverter()
    file_format = converter.get_file_format(file_path)
    file_size = os.path.getsize(file_path)
    
    return {
        'path': file_path,
        'name': os.path.basename(file_path),
        'format': file_format,
        'size': file_size,
        'size_mb': round(file_size / (1024 * 1024), 2),
        'supported': converter.is_supported_input(file_path),
        'needs_conversion': converter.needs_conversion(file_path)
    }
