#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面访问
"""

import requests
import webbrowser
import time

def test_web_interface():
    """测试Web界面"""
    print("🌐 测试Web界面访问")
    print("=" * 30)
    
    base_url = "http://localhost:5000"
    
    # 测试1: 主页访问
    print("1️⃣ 测试主页访问...")
    try:
        response = requests.get(base_url, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 主页访问成功")
            
            # 检查页面内容
            content = response.text
            if "双语书籍翻译服务" in content:
                print("   ✅ 页面标题正确")
            if "api/auth/register" in content or "register" in content.lower():
                print("   ✅ 包含注册功能")
            if "api/auth/login" in content or "login" in content.lower():
                print("   ✅ 包含登录功能")
            
            print(f"   📄 页面大小: {len(content)} 字符")
        else:
            print(f"   ❌ 主页访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 主页访问请求失败: {str(e)}")
    
    # 测试2: 静态资源
    print("\n2️⃣ 测试静态资源...")
    
    # 检查是否有CSS/JS内联
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            content = response.text
            if "<style>" in content:
                print("   ✅ 包含CSS样式")
            if "<script>" in content:
                print("   ✅ 包含JavaScript")
            if "fetch(" in content or "XMLHttpRequest" in content:
                print("   ✅ 包含AJAX功能")
    except Exception as e:
        print(f"   ❌ 静态资源检查失败: {str(e)}")
    
    # 测试3: API端点可访问性
    print("\n3️⃣ 测试API端点...")
    
    api_endpoints = [
        ("/api/auth/register", "POST", "注册接口"),
        ("/api/auth/login", "POST", "登录接口"),
        ("/api/upload", "POST", "上传接口"),
        ("/api/translate", "POST", "翻译接口")
    ]
    
    for endpoint, method, description in api_endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                # 对于POST请求，发送空数据测试端点是否存在
                response = requests.post(f"{base_url}{endpoint}", json={}, timeout=5)
            
            # 400是预期的（因为我们发送了空数据），404表示端点不存在
            if response.status_code != 404:
                print(f"   ✅ {description} 端点存在")
            else:
                print(f"   ❌ {description} 端点不存在")
                
        except Exception as e:
            print(f"   ❌ {description} 测试失败: {str(e)}")
    
    print("\n✅ Web界面测试完成！")
    
    # 询问是否打开浏览器
    try:
        user_input = input("\n🌐 是否在浏览器中打开Web界面？(y/n): ").strip().lower()
        if user_input in ['y', 'yes', '是']:
            print("🚀 正在打开浏览器...")
            webbrowser.open(base_url)
            print("✅ 浏览器已打开，请查看Web界面")
            
            print("\n📝 Web界面使用说明:")
            print("1. 可以直接使用翻译功能（无需登录）")
            print("2. 注册账户可以获得积分管理")
            print("3. 每日签到可以获得额外积分")
            print("4. 支持中英文界面切换")
            
    except KeyboardInterrupt:
        print("\n👋 测试结束")

def test_api_documentation():
    """生成API文档测试"""
    print("\n\n📚 API接口文档")
    print("=" * 40)
    
    api_docs = {
        "用户认证": {
            "POST /api/auth/register": {
                "描述": "用户注册",
                "参数": {"email": "邮箱", "password": "密码"},
                "返回": "用户信息和JWT令牌"
            },
            "POST /api/auth/login": {
                "描述": "用户登录", 
                "参数": {"email": "邮箱", "password": "密码"},
                "返回": "用户信息和JWT令牌"
            },
            "GET /api/auth/me": {
                "描述": "获取当前用户信息",
                "认证": "需要Bearer令牌",
                "返回": "用户详细信息"
            },
            "POST /api/auth/signin": {
                "描述": "每日签到",
                "认证": "需要Bearer令牌", 
                "返回": "签到结果和积分"
            }
        },
        "文件处理": {
            "POST /api/upload": {
                "描述": "上传文件",
                "参数": "multipart/form-data文件",
                "支持格式": "TXT, EPUB, PDF, DOCX等16种",
                "返回": "upload_id和文件信息"
            },
            "POST /api/translate": {
                "描述": "开始翻译任务",
                "认证": "需要登录和足够积分",
                "参数": {
                    "upload_id": "文件ID",
                    "target_language": "目标语言",
                    "style": "翻译风格"
                },
                "返回": "任务ID和状态"
            }
        },
        "任务管理": {
            "GET /api/job/{job_id}": {
                "描述": "查询翻译任务状态",
                "返回": "任务详细信息"
            },
            "GET /api/download/{job_id}/{format}": {
                "描述": "下载翻译结果",
                "格式": "epub, pdf, docx",
                "返回": "文件下载"
            },
            "GET /api/download-info/{job_id}": {
                "描述": "获取下载信息",
                "返回": "可用格式和文件大小"
            }
        }
    }
    
    for category, endpoints in api_docs.items():
        print(f"\n🔹 {category}")
        for endpoint, info in endpoints.items():
            print(f"   {endpoint}")
            for key, value in info.items():
                if isinstance(value, dict):
                    print(f"     {key}:")
                    for k, v in value.items():
                        print(f"       {k}: {v}")
                else:
                    print(f"     {key}: {value}")

if __name__ == "__main__":
    test_web_interface()
    test_api_documentation()
