#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发进度更新工具
用于更新开发计划中的任务完成状态
"""

import os
import re
from datetime import datetime

def update_task_status(file_path, task_description, completed=True):
    """
    更新任务状态
    
    Args:
        file_path: 文件路径
        task_description: 任务描述（用于匹配）
        completed: 是否完成（True为完成，False为未完成）
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找匹配的任务行
    if completed:
        # 将 [ ] 改为 [x]
        pattern = rf'- \[ \] (.*)({re.escape(task_description)}.*)'
        replacement = r'- [x] \1\2'
    else:
        # 将 [x] 改为 [ ]
        pattern = rf'- \[x\] (.*)({re.escape(task_description)}.*)'
        replacement = r'- [ ] \1\2'
    
    new_content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
    
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 已更新任务状态: {task_description}")
        return True
    else:
        print(f"❌ 未找到匹配的任务: {task_description}")
        return False

def add_completion_note(file_path, task_description, note=""):
    """
    添加完成备注
    """
    timestamp = datetime.now().strftime("%Y-%m-%d")
    completion_note = f" (完成于 {timestamp})"
    if note:
        completion_note += f" - {note}"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找已完成的任务并添加备注
    pattern = rf'(- \[x\] .*)({re.escape(task_description)})(.*)'
    replacement = rf'\1\2{completion_note}\3'
    
    new_content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
    
    if new_content != content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"✅ 已添加完成备注: {task_description}")
        return True
    return False

def show_progress_summary():
    """
    显示开发进度摘要
    """
    files_to_check = ['开发计划.md', 'readme.md']
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计完成和未完成的任务
        completed_tasks = len(re.findall(r'- \[x\]', content))
        pending_tasks = len(re.findall(r'- \[ \]', content))
        total_tasks = completed_tasks + pending_tasks
        
        if total_tasks > 0:
            progress_percentage = (completed_tasks / total_tasks) * 100
            print(f"\n📊 {file_path} 进度统计:")
            print(f"   ✅ 已完成: {completed_tasks}")
            print(f"   ⏳ 待完成: {pending_tasks}")
            print(f"   📈 完成率: {progress_percentage:.1f}%")
            print(f"   📋 总任务: {total_tasks}")

def main():
    """
    主函数 - 提供交互式界面
    """
    print("🚀 开发进度更新工具")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 标记任务为完成")
        print("2. 标记任务为未完成")
        print("3. 查看进度摘要")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            task_desc = input("请输入要标记为完成的任务描述关键词: ").strip()
            if task_desc:
                update_task_status('开发计划.md', task_desc, True)
                update_task_status('readme.md', task_desc, True)
                
                note = input("是否添加完成备注 (可选): ").strip()
                if note:
                    add_completion_note('开发计划.md', task_desc, note)
        
        elif choice == '2':
            task_desc = input("请输入要标记为未完成的任务描述关键词: ").strip()
            if task_desc:
                update_task_status('开发计划.md', task_desc, False)
                update_task_status('readme.md', task_desc, False)
        
        elif choice == '3':
            show_progress_summary()
        
        elif choice == '4':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
