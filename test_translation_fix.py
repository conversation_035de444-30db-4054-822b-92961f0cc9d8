#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译功能修复
"""

import os
import sys
import uuid
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.task_queue import TaskQueue
from src.models.translation_job import TranslationJob
from src.generator.multi_format_generator import MultiFormatGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_multi_format_generator():
    """测试多格式生成器"""
    print("\n🔧 测试多格式生成器")
    print("=" * 50)
    
    try:
        # 创建生成器实例
        generator = MultiFormatGenerator()
        logger.info("创建多格式生成器成功")
        
        # 检查方法是否存在
        if hasattr(generator, 'generate_all_formats'):
            logger.info("✅ generate_all_formats 方法存在")
        else:
            logger.error("❌ generate_all_formats 方法不存在")
            return False
        
        # 检查支持的格式
        supported_formats = generator.get_supported_output_formats()
        logger.info(f"支持的输出格式: {supported_formats}")
        
        print("✅ 多格式生成器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"多格式生成器测试失败: {str(e)}")
        print("❌ 多格式生成器测试失败")
        return False

def test_task_queue_method_call():
    """测试任务队列中的方法调用"""
    print("\n⚙️ 测试任务队列方法调用")
    print("=" * 50)
    
    try:
        # 创建测试任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test.txt",
            file_path="test_sample.txt",
            target_language="中文",
            style="casual",
            status="pending"
        )
        job.save()
        logger.info(f"创建测试任务: {job_id}")
        
        # 测试任务队列的处理逻辑（不实际运行）
        queue = TaskQueue()
        logger.info("任务队列创建成功")
        
        # 检查是否能正确导入和调用相关方法
        from src.generator.multi_format_generator import MultiFormatGenerator
        generator = MultiFormatGenerator()
        
        # 模拟方法调用（不实际执行）
        method_exists = hasattr(generator, 'generate_all_formats')
        logger.info(f"generate_all_formats 方法存在: {method_exists}")
        
        if method_exists:
            logger.info("✅ 任务队列方法调用测试通过")
            result = True
        else:
            logger.error("❌ 方法不存在")
            result = False
        
        # 清理测试数据
        try:
            os.remove(f'data/jobs/{job_id}.json')
        except:
            pass
        
        print("✅ 任务队列方法调用测试通过" if result else "❌ 任务队列方法调用测试失败")
        return result
        
    except Exception as e:
        logger.error(f"任务队列方法调用测试失败: {str(e)}")
        print("❌ 任务队列方法调用测试失败")
        return False

def test_import_dependencies():
    """测试依赖导入"""
    print("\n📦 测试依赖导入")
    print("=" * 50)
    
    try:
        # 测试关键模块导入
        from src.utils.task_queue import TaskQueue, add_translation_job
        from src.generator.multi_format_generator import MultiFormatGenerator
        from src.models.translation_job import TranslationJob
        from src.models.points_transaction import PointsTransaction
        
        logger.info("✅ 所有关键模块导入成功")
        print("✅ 依赖导入测试通过")
        return True
        
    except Exception as e:
        logger.error(f"依赖导入测试失败: {str(e)}")
        print("❌ 依赖导入测试失败")
        return False

def main():
    """主测试函数"""
    print("🔧 翻译功能修复验证")
    print("=" * 60)
    
    # 确保目录存在
    os.makedirs('data/jobs', exist_ok=True)
    os.makedirs('data/transactions', exist_ok=True)
    
    # 运行测试
    tests = [
        ("依赖导入", test_import_dependencies),
        ("多格式生成器", test_multi_format_generator),
        ("任务队列方法调用", test_task_queue_method_call),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！翻译功能修复成功")
        print("💡 现在可以启动服务进行翻译了")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
