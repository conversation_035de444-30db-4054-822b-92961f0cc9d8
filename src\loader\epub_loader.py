#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EPUB文件加载器
基于参考实现进行简化
"""

import logging
from copy import copy
from bs4 import BeautifulSoup as bs
from ebooklib import ITEM_DOCUMENT, epub
from tqdm import tqdm
from .base_loader import BaseLoader

logger = logging.getLogger(__name__)


class EPUBLoader(BaseLoader):
    """EPUB文件加载器"""
    
    def __init__(self, file_path, translator):
        super().__init__(file_path, translator)
        self.translate_tags = ["p", "h1", "h2", "h3", "h4", "li", "div"]
        self.exclude_translate_tags = ["sup"]
        
        # 读取EPUB文件
        try:
            self.book = epub.read_epub(file_path)
        except Exception as e:
            logger.error(f"读取EPUB文件失败: {str(e)}")
            raise
    
    def make_bilingual_book(self, output_path):
        """生成双语版本的EPUB文件"""
        logger.info(f"开始处理EPUB文件: {self.file_path}")
        
        try:
            # 创建新的EPUB书籍
            new_book = self._create_new_book()
            
            # 获取所有需要翻译的段落
            all_items = list(self.book.get_items_of_type(ITEM_DOCUMENT))
            total_paragraphs = self._count_paragraphs(all_items)
            
            logger.info(f"共找到 {total_paragraphs} 个段落需要处理")
            
            # 处理每个文档项
            with tqdm(total=total_paragraphs, desc="翻译进度") as pbar:
                for item in all_items:
                    self._process_item(item, new_book, pbar)
            
            # 添加非文档项（图片、样式等）
            for item in self.book.get_items():
                if item.get_type() != ITEM_DOCUMENT:
                    new_book.add_item(item)
            
            # 保存新的EPUB文件
            epub.write_epub(output_path, new_book, {})
            logger.info(f"双语EPUB文件生成完成: {output_path}")
            
        except Exception as e:
            logger.error(f"处理EPUB文件失败: {str(e)}")
            raise
    
    def _create_new_book(self):
        """创建新的EPUB书籍"""
        new_book = epub.EpubBook()
        new_book.metadata = self.book.metadata
        new_book.spine = self.book.spine
        new_book.toc = self.book.toc
        return new_book
    
    def _count_paragraphs(self, items):
        """计算总段落数"""
        total = 0
        for item in items:
            soup = bs(item.content, "html.parser")
            paragraphs = soup.find_all(self.translate_tags)
            total += len([p for p in paragraphs if self._should_translate(p.get_text())])
        return total
    
    def _process_item(self, item, new_book, pbar):
        """处理单个文档项"""
        soup = bs(item.content, "html.parser")
        paragraphs = soup.find_all(self.translate_tags)
        
        for p in paragraphs:
            if self._should_translate(p.get_text()):
                try:
                    self._translate_paragraph(p)
                    pbar.update(1)
                except Exception as e:
                    logger.error(f"段落翻译失败: {str(e)}")
                    pbar.update(1)
        
        # 更新内容
        if soup:
            item.content = soup.encode()
        
        new_book.add_item(item)
    
    def _translate_paragraph(self, paragraph):
        """翻译单个段落"""
        # 创建段落副本用于翻译
        temp_p = copy(paragraph)
        
        # 移除不需要翻译的标签
        for exclude_tag in self.exclude_translate_tags:
            for tag in temp_p.find_all(exclude_tag):
                tag.extract()
        
        original_text = temp_p.get_text().strip()
        if not original_text:
            return
        
        # 翻译文本
        translated_text = self.translator.translate(original_text)
        
        # 插入翻译内容
        self._insert_translation(paragraph, translated_text)
    
    def _insert_translation(self, original_element, translated_text):
        """在原始元素后插入翻译内容"""
        # 创建翻译段落
        translation_p = bs(f'<p style="{self.translation_style}">{translated_text}</p>', 'html.parser').p
        
        # 在原始段落后插入翻译
        original_element.insert_after(translation_p)
