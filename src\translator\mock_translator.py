#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟翻译器 - 用于测试和演示
"""

import time
import logging
from .base_translator import BaseTranslator

logger = logging.getLogger(__name__)


class MockTranslator(BaseTranslator):
    """模拟翻译器 - 用于测试，不调用外部API"""

    def __init__(self, api_key=None, target_language="中文", style='casual'):
        super().__init__("mock_key", target_language, style)
        self.translation_delay = 1  # 模拟翻译延迟
        
    def translate(self, text):
        """模拟翻译文本"""
        if self._is_special_text(text):
            return text

        logger.info(f"模拟翻译: {text[:100]}...")
        
        # 模拟翻译延迟
        time.sleep(self.translation_delay)
        
        # 根据目标语言生成模拟翻译
        if self.target_language == "中文":
            translated = f"[中文翻译] {text}"
        elif self.target_language == "日本語":
            translated = f"[日本語翻訳] {text}"
        elif self.target_language == "Français":
            translated = f"[Traduction française] {text}"
        elif self.target_language == "Español":
            translated = f"[Traducción española] {text}"
        elif self.target_language == "Deutsch":
            translated = f"[Deutsche Übersetzung] {text}"
        elif self.target_language == "한국어":
            translated = f"[한국어 번역] {text}"
        else:
            translated = f"[Translation to {self.target_language}] {text}"
        
        # 根据风格调整
        if self.style == "faithful":
            translated = f"[严谨] {translated}"
        else:
            translated = f"[轻松] {translated}"
        
        logger.info(f"模拟翻译完成: {translated[:100]}...")
        return translated
