#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本
"""

import os
import sys

def test_imports():
    """测试基础导入"""
    print("测试基础导入...")
    
    try:
        from src.translator.gemini_translator import GeminiTranslator
        print("✅ GeminiTranslator 导入成功")
    except ImportError as e:
        print(f"❌ GeminiTranslator 导入失败: {e}")
        return False
    
    try:
        from src.loader.txt_loader import TXTLoader
        print("✅ TXTLoader 导入成功")
    except ImportError as e:
        print(f"❌ TXTLoader 导入失败: {e}")
        return False
    
    try:
        from src.models.translation_job import TranslationJob
        print("✅ TranslationJob 导入成功")
    except ImportError as e:
        print(f"❌ TranslationJob 导入失败: {e}")
        return False
    
    try:
        from src.utils.file_utils import allowed_file, get_file_extension
        print("✅ file_utils 导入成功")
    except ImportError as e:
        print(f"❌ file_utils 导入失败: {e}")
        return False
    
    return True

def test_directories():
    """测试目录创建"""
    print("\n测试目录创建...")
    
    directories = ['uploads', 'outputs', 'data', 'data/jobs', 'data/users']
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ 目录创建成功: {directory}")
        except Exception as e:
            print(f"❌ 目录创建失败 {directory}: {e}")
            return False
    
    return True

def test_txt_translation():
    """测试TXT翻译功能（模拟）"""
    print("\n测试TXT翻译功能...")
    
    try:
        from src.translator.base_translator import BaseTranslator
        from src.loader.txt_loader import TXTLoader
        
        # 创建模拟翻译器
        class MockTranslator(BaseTranslator):
            def translate(self, text):
                return f"[翻译] {text}"
        
        # 创建测试文件
        test_content = """这是第一段测试内容。

这是第二段测试内容。

这是第三段测试内容。"""
        
        test_file = "test_input.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 测试加载器
        translator = MockTranslator("mock_key", "中文")
        loader = TXTLoader(test_file, translator)
        
        output_file = "test_output.txt"
        loader.make_bilingual_book(output_file)
        
        # 检查输出
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                result = f.read()
            print("✅ TXT翻译测试成功")
            print(f"输出预览: {result[:100]}...")
            
            # 清理测试文件
            os.remove(test_file)
            os.remove(output_file)
            return True
        else:
            print("❌ 输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ TXT翻译测试失败: {e}")
        return False

def test_translation_job():
    """测试翻译任务模型"""
    print("\n测试翻译任务模型...")
    
    try:
        from src.models.translation_job import TranslationJob
        
        # 创建任务
        job = TranslationJob(
            job_id="test-job-123",
            upload_id="test-upload-123",
            original_filename="test.txt",
            file_path="/path/to/test.txt",
            target_language="中文",
            style="casual"
        )
        
        # 保存任务
        job.save()
        
        # 加载任务
        loaded_job = TranslationJob.load("test-job-123")
        
        if loaded_job and loaded_job.job_id == "test-job-123":
            print("✅ 翻译任务模型测试成功")
            
            # 清理测试数据
            import os
            test_file = "data/jobs/test-job-123.json"
            if os.path.exists(test_file):
                os.remove(test_file)
            
            return True
        else:
            print("❌ 任务加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 翻译任务模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始基础功能测试...\n")
    
    tests = [
        test_imports,
        test_directories,
        test_txt_translation,
        test_translation_job
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
