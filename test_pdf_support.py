#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF文件支持功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.loader.multi_format_loader import MultiFormatLoader, get_file_info

def test_pdf_support():
    """测试PDF文件支持"""
    print("📄 测试PDF文件支持功能...")
    
    # 查找PDF文件
    pdf_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if not pdf_files:
        print("❌ 没有找到PDF文件进行测试")
        return
    
    # 测试第一个找到的PDF文件
    test_file = pdf_files[0]
    print(f"📁 测试文件: {test_file}")
    
    try:
        # 获取文件信息
        file_info = get_file_info(test_file)
        print(f"📊 文件信息: {file_info}")
        
        # 测试加载
        loader = MultiFormatLoader()
        print(f"🔧 格式支持: {loader.is_supported(test_file)}")
        
        # 加载文件内容
        print("📖 开始加载PDF内容...")
        chapters, original_format = loader.load_file(test_file)
        
        print(f"✅ 加载成功!")
        print(f"   📄 原始格式: {original_format}")
        print(f"   📚 章节数量: {len(chapters)}")
        print(f"   📝 第一章节预览: {chapters[0][:200]}...")
        
        if len(chapters) > 1:
            print(f"   📝 第二章节预览: {chapters[1][:200]}...")
        
        # 清理
        loader.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_pdf_libraries():
    """测试PDF处理库"""
    print("\n🔧 测试PDF处理库...")
    
    # 测试pdfplumber
    try:
        import pdfplumber
        print("✅ pdfplumber导入成功")
    except ImportError:
        print("❌ pdfplumber未安装")
    
    # 测试PyPDF2
    try:
        import PyPDF2
        print("✅ PyPDF2导入成功")
    except ImportError:
        print("❌ PyPDF2未安装")
    
    # 查找PDF文件进行实际测试
    pdf_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    if pdf_files:
        test_file = pdf_files[0]
        print(f"📁 测试文件: {test_file}")
        
        # 测试pdfplumber
        try:
            import pdfplumber
            with pdfplumber.open(test_file) as pdf:
                page_count = len(pdf.pages)
                first_page_text = pdf.pages[0].extract_text()[:100] if pdf.pages else ""
                print(f"✅ pdfplumber读取成功: {page_count}页")
                print(f"   📝 首页内容预览: {first_page_text}...")
        except Exception as e:
            print(f"❌ pdfplumber读取失败: {str(e)}")
        
        # 测试PyPDF2
        try:
            import PyPDF2
            with open(test_file, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                page_count = len(pdf_reader.pages)
                first_page_text = pdf_reader.pages[0].extract_text()[:100] if pdf_reader.pages else ""
                print(f"✅ PyPDF2读取成功: {page_count}页")
                print(f"   📝 首页内容预览: {first_page_text}...")
        except Exception as e:
            print(f"❌ PyPDF2读取失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 PDF文件支持测试")
    print("=" * 50)
    
    test_pdf_libraries()
    test_pdf_support()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
