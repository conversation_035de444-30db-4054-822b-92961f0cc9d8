#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证工具模块
"""

import re
from functools import wraps
from flask import request, jsonify, current_app
from src.models.user import User


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_password(password: str) -> tuple[bool, str]:
    """
    验证密码强度
    返回: (是否有效, 错误信息)
    """
    if len(password) < 6:
        return False, "密码长度至少6位"
    
    if len(password) > 128:
        return False, "密码长度不能超过128位"
    
    # 检查是否包含至少一个字母和一个数字
    has_letter = any(c.isalpha() for c in password)
    has_digit = any(c.isdigit() for c in password)
    
    if not (has_letter and has_digit):
        return False, "密码必须包含至少一个字母和一个数字"
    
    return True, ""


def get_current_user() -> User:
    """从请求中获取当前用户"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return None
    
    token = auth_header.split(' ')[1]
    payload = User.verify_jwt_token(token, current_app.config['SECRET_KEY'])
    
    if not payload:
        return None
    
    user = User.load(payload['user_id'])
    if not user or not user.is_active:
        return None
    
    return user


def require_auth(f):
    """需要认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            return jsonify({'error': '需要登录'}), 401
        
        # 将用户对象传递给视图函数
        return f(user, *args, **kwargs)
    
    return decorated_function


def optional_auth(f):
    """可选认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        # 将用户对象传递给视图函数（可能为None）
        return f(user, *args, **kwargs)
    
    return decorated_function


def check_points_sufficient(user: User, required_points: int) -> tuple[bool, str]:
    """
    检查用户积分是否足够
    返回: (是否足够, 错误信息)
    """
    if user.points_balance < required_points:
        return False, f"积分不足，需要{required_points}积分，当前余额{user.points_balance}积分"
    return True, ""


def deduct_points_for_translation(user: User, job_id: str = None) -> tuple[bool, int, str]:
    """
    为翻译扣除积分
    新规则：每本书固定50积分
    返回: (是否成功, 扣除的积分数, 错误信息)
    """
    required_points = 50  # 每本书固定50积分

    sufficient, error_msg = check_points_sufficient(user, required_points)
    if not sufficient:
        return False, required_points, error_msg

    success = user.deduct_points(
        amount=required_points,
        transaction_type='translation',
        description='书籍翻译费用',
        related_id=job_id
    )
    if success:
        user.save()
        return True, required_points, ""
    else:
        return False, required_points, "积分扣除失败"
