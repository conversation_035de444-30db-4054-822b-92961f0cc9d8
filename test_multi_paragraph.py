#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多段落双语文档生成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.generator.multi_format_generator import MultiFormatGenerator

def test_multi_paragraph_generation():
    """测试多段落生成"""
    print("🚀 测试多段落双语文档生成...")
    
    # 模拟您的PDF内容 - 多个段落
    original_chapters = [
        "The Art of Programming",
        "Programming is both an art and a science. It requires creativity, logical thinking, and attention to detail.",
        "When we write code, we are essentially creating instructions for a computer to follow. These instructions must be precise and unambiguous.",
        "Good programming practices include writing clean, readable code, using meaningful variable names, and adding comments to explain complex logic.",
        "Testing is an essential part of the development process. It helps us identify and fix bugs before they reach production."
    ]
    
    translated_chapters = [
        "编程的艺术",
        "编程既是艺术也是科学。它需要创造力、逻辑思维和对细节的关注。",
        "当我们编写代码时，我们实际上是在为计算机创建要遵循的指令。这些指令必须精确且明确。",
        "良好的编程实践包括编写干净、可读的代码，使用有意义的变量名，以及添加注释来解释复杂的逻辑。",
        "测试是开发过程的重要组成部分。它帮助我们在错误到达生产环境之前识别和修复它们。"
    ]
    
    # 创建输出目录
    output_dir = "test_multi_paragraph_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建生成器
        generator = MultiFormatGenerator()
        
        print("📚 测试多段落格式生成...")
        
        # 测试所有格式
        formats = ['txt', 'docx', 'pdf']
        
        for fmt in formats:
            print(f"📄 测试{fmt.upper()}格式...")
            
            if fmt == 'txt':
                success = generator._generate_txt_format(
                    original_chapters, translated_chapters, f"{output_dir}/multi_paragraph.{fmt}"
                )
            elif fmt == 'docx':
                success = generator._generate_docx_format(
                    original_chapters, translated_chapters, f"{output_dir}/multi_paragraph.{fmt}"
                )
            elif fmt == 'pdf':
                success = generator._generate_pdf_format(
                    original_chapters, translated_chapters, f"{output_dir}/multi_paragraph.{fmt}"
                )
            
            print(f"   {fmt.upper()}生成: {'✅ 成功' if success else '❌ 失败'}")
        
        # 检查生成的文件
        print("\n📊 检查生成的文件...")
        for fmt in formats:
            file_path = f"{output_dir}/multi_paragraph.{fmt}"
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   📄 {fmt.upper()}: {file_path} ({size} bytes)")
            else:
                print(f"   ❌ {fmt.upper()}: 文件不存在")
        
        # 检查TXT文件内容
        txt_file = f"{output_dir}/multi_paragraph.txt"
        if os.path.exists(txt_file):
            print("\n📝 TXT文件内容预览:")
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   内容长度: {len(content)} 字符")
                print(f"   段落数量: {content.count('---') + 1}")
                
                # 检查是否包含不需要的标题
                unwanted_titles = ["双语文档", "第一章", "第二章", "第三章", "第四章", "第五章"]
                found_titles = [title for title in unwanted_titles if title in content]
                
                if found_titles:
                    print(f"   ⚠️  发现不需要的标题: {found_titles}")
                else:
                    print("   ✅ 无不需要的标题")
                
                print(f"   前300字符: {content[:300]}...")
        
        # 清理
        generator.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 多段落双语文档生成测试")
    print("=" * 50)
    
    test_multi_paragraph_generation()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
