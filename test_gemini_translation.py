#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Gemini翻译功能 - 测试不同风格和语言
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.translator.gemini_translator import GeminiTranslator

def test_translation_styles():
    """测试不同翻译风格"""
    print("开始测试Gemini翻译功能 - 不同风格和语言...")

    # 测试文本
    test_text = "Artificial intelligence is revolutionizing the way we approach complex problems in science and technology."

    # 测试配置
    test_configs = [
        {"language": "中文", "style": "casual", "desc": "中文-轻松风格"},
        {"language": "中文", "style": "faithful", "desc": "中文-严谨忠实风格"},
        {"language": "日本語", "style": "casual", "desc": "日语-轻松风格"},
        {"language": "日本語", "style": "faithful", "desc": "日语-严谨忠实风格"},
        {"language": "Français", "style": "casual", "desc": "法语-轻松风格"},
        {"language": "Español", "style": "faithful", "desc": "西班牙语-严谨忠实风格"},
    ]

    print(f"测试文本: {test_text}")
    print("=" * 80)

    for config in test_configs:
        try:
            print(f"\n【{config['desc']}】")
            translator = GeminiTranslator(target_language=config['language'], style=config['style'])
            result = translator.translate(test_text)
            print(f"翻译结果: {result}")
            print("-" * 60)
        except Exception as e:
            print(f"翻译失败: {str(e)}")
            print("-" * 60)

def test_basic_functionality():
    """测试基本功能"""
    print("\n开始测试基本翻译功能...")

    # 初始化翻译器
    translator = GeminiTranslator(target_language="中文", style="casual")

    # 测试文本
    test_texts = [
        "Hello, world!",
        "This is a test of the Gemini translation system.",
        "The quick brown fox jumps over the lazy dog."
    ]

    print(f"使用API端点: {translator.API_URL}")
    print(f"使用API密钥: {translator.API_KEY}")
    print("-" * 50)

    for i, text in enumerate(test_texts, 1):
        try:
            print(f"\n测试 {i}: {text}")
            result = translator.translate(text)
            print(f"翻译结果: {result}")
            print("-" * 30)
        except Exception as e:
            print(f"翻译失败: {str(e)}")
            print("-" * 30)

if __name__ == "__main__":
    test_translation_styles()
    test_basic_functionality()
