#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户认证系统
"""

import sys
import os
import requests
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_user_registration():
    """测试用户注册"""
    print("🔐 测试用户注册...")
    
    url = "http://localhost:5000/api/auth/register"
    data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 201:
            print("✅ 用户注册成功")
            return response.json().get('token')
        else:
            print("❌ 用户注册失败")
            return None
            
    except Exception as e:
        print(f"❌ 注册请求失败: {str(e)}")
        return None

def test_user_login():
    """测试用户登录"""
    print("\n🔑 测试用户登录...")
    
    url = "http://localhost:5000/api/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 用户登录成功")
            return response.json().get('token')
        else:
            print("❌ 用户登录失败")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None

def test_get_user_info(token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    
    if not token:
        print("❌ 没有有效的令牌")
        return
    
    url = "http://localhost:5000/api/auth/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 获取用户信息成功")
        else:
            print("❌ 获取用户信息失败")
            
    except Exception as e:
        print(f"❌ 获取用户信息请求失败: {str(e)}")

def test_daily_signin(token):
    """测试每日签到"""
    print("\n📅 测试每日签到...")
    
    if not token:
        print("❌ 没有有效的令牌")
        return
    
    url = "http://localhost:5000/api/auth/signin"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(url, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        if response.status_code == 200:
            print("✅ 每日签到成功")
        else:
            print("❌ 每日签到失败")
            
    except Exception as e:
        print(f"❌ 签到请求失败: {str(e)}")

def test_translation_with_auth(token):
    """测试带认证的翻译功能"""
    print("\n📚 测试带认证的翻译功能...")
    
    if not token:
        print("❌ 没有有效的令牌")
        return
    
    # 首先上传一个测试文件
    upload_url = "http://localhost:5000/api/upload"
    
    # 创建一个简单的测试文件
    test_content = "Hello world! This is a test file for translation."
    
    try:
        files = {'file': ('test.txt', test_content, 'text/plain')}
        upload_response = requests.post(upload_url, files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.json()}")
            return
        
        upload_data = upload_response.json()
        upload_id = upload_data['upload_id']
        print(f"✅ 文件上传成功，upload_id: {upload_id}")
        
        # 开始翻译
        translate_url = "http://localhost:5000/api/translate"
        headers = {"Authorization": f"Bearer {token}"}
        translate_data = {
            "upload_id": upload_id,
            "target_language": "中文",
            "style": "casual"
        }
        
        translate_response = requests.post(translate_url, json=translate_data, headers=headers)
        print(f"翻译状态码: {translate_response.status_code}")
        print(f"翻译响应: {translate_response.json()}")
        
        if translate_response.status_code == 200:
            print("✅ 翻译任务创建成功")
        else:
            print("❌ 翻译任务创建失败")
            
    except Exception as e:
        print(f"❌ 翻译测试失败: {str(e)}")

def test_password_validation():
    """测试密码验证"""
    print("\n🔒 测试密码验证...")
    
    # 测试弱密码
    weak_passwords = [
        "123",  # 太短
        "password",  # 只有字母
        "123456",  # 只有数字
        "a" * 130  # 太长
    ]
    
    for password in weak_passwords:
        url = "http://localhost:5000/api/auth/register"
        data = {
            "email": f"test_{len(password)}@example.com",
            "password": password
        }
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 400:
                print(f"✅ 正确拒绝弱密码: {password[:10]}...")
            else:
                print(f"❌ 应该拒绝弱密码: {password[:10]}...")
                
        except Exception as e:
            print(f"❌ 密码验证测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 用户认证系统测试")
    print("=" * 50)
    
    # 测试密码验证
    test_password_validation()
    
    # 测试用户注册
    token = test_user_registration()
    
    # 如果注册失败，尝试登录
    if not token:
        token = test_user_login()
    
    # 测试其他功能
    if token:
        test_get_user_info(token)
        test_daily_signin(token)
        test_translation_with_auth(token)
    
    print("\n✅ 用户认证系统测试完成！")
    print("\n📝 测试说明:")
    print("1. 确保服务器正在运行 (python app.py)")
    print("2. 如果测试失败，检查服务器日志")
    print("3. 用户数据存储在 data/users/ 目录")

if __name__ == "__main__":
    main()
