#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端翻译测试
"""

import os
import sys
import uuid
import time
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.user import User
from src.models.translation_job import TranslationJob
from src.utils.task_queue import TaskQueue
from src.utils.auth_utils import deduct_points_for_translation

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_complete_translation_flow():
    """测试完整的翻译流程"""
    print("\n🔄 测试完整翻译流程")
    print("=" * 50)
    
    try:
        # 1. 创建测试用户
        test_email = "<EMAIL>"
        test_user = User.create_user(test_email, "password123")
        logger.info(f"创建测试用户: {test_email}, 初始积分: {test_user.points_balance}")
        
        # 2. 创建翻译任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test_sample.txt",
            file_path="test_sample.txt",
            target_language="English",
            style="casual",
            status="pending"
        )
        job.user_id = test_user.user_id
        job.cost_points = 50
        job.save()
        logger.info(f"创建翻译任务: {job_id}")
        
        # 3. 扣除积分
        success, deducted, error = deduct_points_for_translation(test_user, job_id)
        if success:
            logger.info(f"积分扣除成功: {deducted} 积分")
            logger.info(f"用户剩余积分: {test_user.points_balance}")
        else:
            logger.error(f"积分扣除失败: {error}")
            return False
        
        # 4. 测试任务队列处理（模拟）
        queue = TaskQueue()
        logger.info("任务队列创建成功")
        
        # 检查任务是否可以被正确加载
        loaded_job = TranslationJob.load(job_id)
        if loaded_job:
            logger.info(f"任务加载成功: {loaded_job.status}")
        else:
            logger.error("任务加载失败")
            return False
        
        # 5. 模拟任务处理过程
        loaded_job.status = 'processing'
        loaded_job.save()
        logger.info("任务状态更新为处理中")
        
        # 模拟处理完成
        loaded_job.status = 'completed'
        loaded_job.output_path = f"outputs/{job_id}"
        loaded_job.output_files = {
            'epub': f"outputs/{job_id}/test_sample_bilingual.epub",
            'pdf': f"outputs/{job_id}/test_sample_bilingual.pdf",
            'docx': f"outputs/{job_id}/test_sample_bilingual.docx"
        }
        loaded_job.save()
        logger.info("任务状态更新为已完成")
        
        # 6. 验证结果
        final_job = TranslationJob.load(job_id)
        if final_job and final_job.status == 'completed':
            logger.info("✅ 翻译流程测试成功")
            logger.info(f"输出文件: {final_job.output_files}")
            result = True
        else:
            logger.error("❌ 翻译流程测试失败")
            result = False
        
        # 7. 清理测试数据
        try:
            os.remove(f'data/users/{test_user.user_id}.json')
            os.remove(f'data/jobs/{job_id}.json')
            # 清理积分交易记录
            from src.models.points_transaction import PointsTransaction
            transactions = PointsTransaction.get_user_transactions(test_user.user_id)
            for t in transactions:
                try:
                    os.remove(f'data/transactions/{t.transaction_id}.json')
                except:
                    pass
        except Exception as e:
            logger.warning(f"清理测试数据时出现警告: {str(e)}")
        
        print("✅ 完整翻译流程测试通过" if result else "❌ 完整翻译流程测试失败")
        return result
        
    except Exception as e:
        logger.error(f"完整翻译流程测试失败: {str(e)}")
        print("❌ 完整翻译流程测试失败")
        return False

def test_task_queue_functionality():
    """测试任务队列功能"""
    print("\n⚙️ 测试任务队列功能")
    print("=" * 50)
    
    try:
        # 创建任务队列
        queue = TaskQueue()
        logger.info("任务队列创建成功")
        
        # 测试添加任务
        test_job_id = str(uuid.uuid4())
        queue.add_job(test_job_id)
        logger.info(f"任务添加成功: {test_job_id}")
        
        # 检查队列大小
        queue_size = queue.queue.qsize()
        logger.info(f"队列大小: {queue_size}")
        
        if queue_size > 0:
            logger.info("✅ 任务队列功能正常")
            result = True
        else:
            logger.error("❌ 任务队列功能异常")
            result = False
        
        print("✅ 任务队列功能测试通过" if result else "❌ 任务队列功能测试失败")
        return result
        
    except Exception as e:
        logger.error(f"任务队列功能测试失败: {str(e)}")
        print("❌ 任务队列功能测试失败")
        return False

def main():
    """主测试函数"""
    print("🧪 端到端翻译测试")
    print("=" * 60)
    
    # 确保目录存在
    required_dirs = ['data/users', 'data/jobs', 'data/transactions', 'outputs']
    for dir_path in required_dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    # 运行测试
    tests = [
        ("任务队列功能", test_task_queue_functionality),
        ("完整翻译流程", test_complete_translation_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！翻译服务已就绪")
        print("🚀 可以启动服务开始使用了")
        print("💡 运行命令: python start_with_queue.py")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
