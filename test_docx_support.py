#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DOCX文件支持功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.loader.multi_format_loader import MultiFormatLoader, get_file_info

def test_docx_support():
    """测试DOCX文件支持"""
    print("📄 测试DOCX文件支持功能...")
    
    # 查找DOCX文件
    docx_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.lower().endswith('.docx'):
                docx_files.append(os.path.join(root, file))
    
    if not docx_files:
        print("❌ 没有找到DOCX文件进行测试")
        return
    
    # 测试第一个找到的DOCX文件
    test_file = docx_files[0]
    print(f"📁 测试文件: {test_file}")
    
    try:
        # 获取文件信息
        file_info = get_file_info(test_file)
        print(f"📊 文件信息: {file_info}")
        
        # 测试加载
        loader = MultiFormatLoader()
        print(f"🔧 格式支持: {loader.is_supported(test_file)}")
        
        # 加载文件内容
        print("📖 开始加载DOCX内容...")
        chapters, original_format = loader.load_file(test_file)
        
        print(f"✅ 加载成功!")
        print(f"   📄 原始格式: {original_format}")
        print(f"   📚 章节数量: {len(chapters)}")
        print(f"   📝 第一章节预览: {chapters[0][:200]}...")
        
        if len(chapters) > 1:
            print(f"   📝 第二章节预览: {chapters[1][:200]}...")
        
        # 清理
        loader.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_docx_direct():
    """直接测试python-docx功能"""
    print("\n🔧 测试python-docx直接功能...")
    
    try:
        from docx import Document
        print("✅ python-docx导入成功")
        
        # 查找DOCX文件
        docx_files = []
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.lower().endswith('.docx'):
                    docx_files.append(os.path.join(root, file))
        
        if docx_files:
            test_file = docx_files[0]
            print(f"📁 测试文件: {test_file}")
            
            doc = Document(test_file)
            paragraphs = [p.text.strip() for p in doc.paragraphs if p.text.strip()]
            
            print(f"✅ 直接读取成功!")
            print(f"   📚 段落数量: {len(paragraphs)}")
            if paragraphs:
                print(f"   📝 第一段落: {paragraphs[0][:200]}...")
        
    except ImportError:
        print("❌ python-docx未安装")
    except Exception as e:
        print(f"❌ 直接测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 DOCX文件支持测试")
    print("=" * 50)
    
    test_docx_direct()
    test_docx_support()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
