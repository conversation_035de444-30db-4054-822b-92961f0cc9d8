#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多格式生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.generator.multi_format_generator import MultiFormatGenerator

def test_multi_format_generation():
    """测试多格式生成功能"""
    print("🚀 测试多格式生成功能...")
    
    # 测试数据
    original_chapters = [
        "Chapter 1: The Beginning",
        "This is the first chapter of our test book. It contains some sample text to test the translation and formatting capabilities.",
        "Chapter 2: The Middle", 
        "This is the second chapter with more content to demonstrate the multi-format generation."
    ]
    
    translated_chapters = [
        "第一章：开始",
        "这是我们测试书籍的第一章。它包含一些示例文本来测试翻译和格式化功能。",
        "第二章：中间",
        "这是第二章，包含更多内容来演示多格式生成。"
    ]
    
    # 创建输出目录
    output_dir = "test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建生成器
        generator = MultiFormatGenerator()
        
        # 测试生成多种格式
        print("📚 生成多种格式...")
        results = generator.generate_all_formats(
            original_chapters=original_chapters,
            translated_chapters=translated_chapters,
            output_dir=output_dir,
            base_filename="test_book",
            target_formats=['epub', 'pdf', 'docx', 'txt']
        )
        
        print(f"✅ 生成完成！成功生成 {len(results)} 种格式:")
        for fmt, path in results.items():
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"   📄 {fmt.upper()}: {path} ({size} bytes)")
            else:
                print(f"   ❌ {fmt.upper()}: 文件不存在")
        
        # 测试文件大小估算
        print("\n📊 测试文件大小估算...")
        estimates = generator.estimate_file_sizes(
            original_chapters, translated_chapters, ['epub', 'pdf', 'docx', 'txt']
        )
        
        for fmt, size in estimates.items():
            print(f"   📏 {fmt.upper()}: {size}")
        
        # 清理
        generator.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_individual_formats():
    """测试单独的格式生成"""
    print("\n🔧 测试单独格式生成...")
    
    original_chapters = ["Test Chapter"]
    translated_chapters = ["测试章节"]
    
    generator = MultiFormatGenerator()
    
    # 创建测试目录
    os.makedirs("test_output", exist_ok=True)

    # 测试TXT格式
    print("📝 测试TXT格式...")
    try:
        success = generator._generate_txt_format(
            original_chapters, translated_chapters, "test_output/test.txt"
        )
        print(f"   TXT生成: {'✅ 成功' if success else '❌ 失败'}")
    except Exception as e:
        print(f"   TXT生成: ❌ 错误 - {str(e)}")
    
    # 测试DOCX格式
    print("📄 测试DOCX格式...")
    try:
        success = generator._generate_docx_format(
            original_chapters, translated_chapters, "test_output/test.docx"
        )
        print(f"   DOCX生成: {'✅ 成功' if success else '❌ 失败'}")
    except Exception as e:
        print(f"   DOCX生成: ❌ 错误 - {str(e)}")
    
    # 测试PDF格式
    print("📋 测试PDF格式...")
    try:
        success = generator._generate_pdf_format(
            original_chapters, translated_chapters, "test_output/test.pdf"
        )
        print(f"   PDF生成: {'✅ 成功' if success else '❌ 失败'}")
    except Exception as e:
        print(f"   PDF生成: ❌ 错误 - {str(e)}")

def main():
    """主测试函数"""
    print("🚀 多格式生成功能测试")
    print("=" * 50)
    
    test_individual_formats()
    test_multi_format_generation()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
