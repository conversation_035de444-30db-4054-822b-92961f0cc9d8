#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式转换器
支持多种电子书格式转换为EPUB，以及输出多种格式
"""

import os
import subprocess
import shutil
import tempfile
import logging
from pathlib import Path
from typing import Optional, List, Dict

logger = logging.getLogger(__name__)


class FormatConverter:
    """格式转换器类"""
    
    # 支持的输入格式
    SUPPORTED_INPUT_FORMATS = {
        'epub', 'txt', 'pdf', 'docx', 'doc', 'mobi', 'azw', 'azw3', 
        'fb2', 'html', 'htm', 'rtf', 'odt', 'pdb', 'lit', 'lrf'
    }
    
    # 支持的输出格式
    SUPPORTED_OUTPUT_FORMATS = {
        'epub', 'pdf', 'docx', 'txt', 'mobi', 'azw3'
    }
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化转换器
        
        Args:
            temp_dir: 临时目录路径，如果为None则使用系统临时目录
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.calibre_available = self._check_calibre()
        
    def _check_calibre(self) -> bool:
        """检查Calibre是否可用"""
        try:
            result = subprocess.run(['ebook-convert', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("Calibre ebook-convert 可用")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass
        
        logger.warning("Calibre ebook-convert 不可用，某些格式转换功能将受限")
        return False
    
    def is_supported_input(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        ext = Path(file_path).suffix.lower().lstrip('.')
        return ext in self.SUPPORTED_INPUT_FORMATS
    
    def get_file_format(self, file_path: str) -> str:
        """获取文件格式"""
        return Path(file_path).suffix.lower().lstrip('.')
    
    def needs_conversion(self, file_path: str) -> bool:
        """判断文件是否需要转换为EPUB"""
        format_type = self.get_file_format(file_path)
        return format_type not in ['epub', 'txt']
    
    def convert_to_epub(self, input_path: str, output_path: str) -> bool:
        """
        将文件转换为EPUB格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出EPUB文件路径
            
        Returns:
            bool: 转换是否成功
        """
        if not self.calibre_available:
            logger.error("Calibre不可用，无法进行格式转换")
            return False
        
        if not os.path.exists(input_path):
            logger.error(f"输入文件不存在: {input_path}")
            return False
        
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 构建转换命令
            cmd = [
                'ebook-convert',
                input_path,
                output_path,
                '--enable-heuristics',  # 启用启发式处理
                '--markup-chapter-headings',  # 标记章节标题
                '--remove-paragraph-spacing',  # 移除段落间距
                '--insert-blank-line',  # 插入空行
                '--linearize-tables',  # 线性化表格
            ]
            
            logger.info(f"开始转换: {input_path} -> {output_path}")
            
            # 执行转换
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"转换成功: {output_path}")
                return True
            else:
                logger.error(f"转换失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("转换超时")
            return False
        except Exception as e:
            logger.error(f"转换过程中发生错误: {str(e)}")
            return False
    
    def convert_epub_to_format(self, epub_path: str, output_path: str, 
                              target_format: str) -> bool:
        """
        将EPUB转换为指定格式
        
        Args:
            epub_path: EPUB文件路径
            output_path: 输出文件路径
            target_format: 目标格式 (pdf, docx, txt等)
            
        Returns:
            bool: 转换是否成功
        """
        if not self.calibre_available:
            logger.error("Calibre不可用，无法进行格式转换")
            return False
        
        if target_format not in self.SUPPORTED_OUTPUT_FORMATS:
            logger.error(f"不支持的输出格式: {target_format}")
            return False
        
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 构建转换命令
            cmd = ['ebook-convert', epub_path, output_path]
            
            # 根据目标格式添加特定参数
            if target_format == 'pdf':
                cmd.extend([
                    '--pdf-page-numbers',  # 添加页码
                    '--preserve-cover-aspect-ratio',  # 保持封面比例
                    '--pdf-serif-family', 'Times New Roman',  # 设置字体
                    '--pdf-sans-family', 'Arial',
                    '--pdf-mono-family', 'Courier New',
                ])
            elif target_format == 'docx':
                cmd.extend([
                    '--preserve-cover-aspect-ratio',
                    '--docx-page-size', 'a4',
                ])
            elif target_format == 'txt':
                cmd.extend([
                    '--txt-output-encoding', 'utf-8',
                    '--max-line-length', '80',
                ])
            
            logger.info(f"开始转换: {epub_path} -> {output_path} ({target_format})")
            
            # 执行转换
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"转换成功: {output_path}")
                return True
            else:
                logger.error(f"转换失败: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("转换超时")
            return False
        except Exception as e:
            logger.error(f"转换过程中发生错误: {str(e)}")
            return False
    
    def batch_convert_outputs(self, epub_path: str, output_dir: str, 
                             formats: List[str]) -> Dict[str, str]:
        """
        批量生成多种格式的输出文件
        
        Args:
            epub_path: 源EPUB文件路径
            output_dir: 输出目录
            formats: 要生成的格式列表
            
        Returns:
            Dict[str, str]: 格式到文件路径的映射
        """
        results = {}
        base_name = Path(epub_path).stem
        
        for fmt in formats:
            if fmt in self.SUPPORTED_OUTPUT_FORMATS:
                output_path = os.path.join(output_dir, f"{base_name}.{fmt}")
                if self.convert_epub_to_format(epub_path, output_path, fmt):
                    results[fmt] = output_path
                else:
                    logger.warning(f"生成{fmt}格式失败")
            else:
                logger.warning(f"不支持的输出格式: {fmt}")
        
        return results
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """获取支持的格式列表"""
        return {
            'input': list(self.SUPPORTED_INPUT_FORMATS),
            'output': list(self.SUPPORTED_OUTPUT_FORMATS)
        }
    
    def cleanup_temp_files(self, file_paths: List[str]):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"已删除临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件失败 {file_path}: {str(e)}")


def install_calibre_guide():
    """返回Calibre安装指南"""
    return """
    Calibre安装指南:
    
    Windows:
    1. 访问 https://calibre-ebook.com/download
    2. 下载Windows版本并安装
    3. 确保安装路径添加到系统PATH中
    
    Linux (Ubuntu/Debian):
    sudo apt-get install calibre
    
    Linux (CentOS/RHEL):
    sudo yum install calibre
    
    macOS:
    brew install calibre
    
    或者使用官方安装脚本:
    sudo -v && wget -nv -O- https://download.calibre-ebook.com/linux-installer.sh | sudo sh /dev/stdin
    """
