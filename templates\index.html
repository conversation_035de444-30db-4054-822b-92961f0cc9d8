<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page-title">双语书籍翻译服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #ddd;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress {
            width: 100%;
            height: 8px;
            background-color: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        
        .download-link:hover {
            background-color: #218838;
        }
        
        .hidden {
            display: none;
        }

        .language-switcher {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .lang-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .lang-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .lang-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        /* 用户认证相关样式 */
        .user-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .user-info {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            display: none;
        }

        .user-info.show {
            display: block;
        }

        .auth-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            text-decoration: none;
        }

        .auth-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .auth-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .auth-modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .close:hover {
            color: #000;
        }

        .auth-form {
            margin-top: 20px;
        }

        .auth-form h2 {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }

        .auth-form .form-group {
            margin-bottom: 15px;
        }

        .auth-form .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
        }

        .auth-form .btn {
            width: 100%;
            margin-top: 10px;
        }

        .auth-switch {
            text-align: center;
            margin-top: 15px;
            color: #666;
        }

        .auth-switch a {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }

        .auth-switch a:hover {
            text-decoration: underline;
        }

        .points-display {
            background: rgba(255, 215, 0, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            margin-left: 10px;
        }

        .signin-btn {
            background: rgba(0, 255, 0, 0.2);
            color: white;
            border: 1px solid rgba(0, 255, 0, 0.3);
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }

        .signin-btn:hover {
            background: rgba(0, 255, 0, 0.3);
        }

        /* 任务管理面板样式 */
        .task-panel {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .task-panel:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .task-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .task-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 900px;
            position: relative;
            max-height: 80vh;
            overflow-y: auto;
        }

        .task-list {
            margin-top: 20px;
        }

        .task-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .task-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .task-title {
            font-weight: bold;
            color: #333;
            flex: 1;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .task-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .task-status.processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .task-status.completed {
            background: #d4edda;
            color: #155724;
        }

        .task-status.failed {
            background: #f8d7da;
            color: #721c24;
        }

        .task-details {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
        }

        .task-actions {
            margin-top: 10px;
        }

        .task-actions .btn {
            padding: 6px 12px;
            font-size: 12px;
            margin-right: 10px;
            width: auto;
        }

        .points-history {
            margin-top: 20px;
        }

        .points-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .points-item:last-child {
            border-bottom: none;
        }

        .points-amount {
            font-weight: bold;
        }

        .points-amount.positive {
            color: #28a745;
        }

        .points-amount.negative {
            color: #dc3545;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 16px;
        }

        .tab-btn.active {
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- 用户面板 -->
    <div class="user-panel">
        <div class="user-info" id="userInfo">
            <span data-i18n="welcome">欢迎，</span><span id="username"></span>
            <span class="points-display" id="pointsDisplay">
                <span data-i18n="points">积分：</span><span id="userPoints">0</span>
            </span>
            <button class="signin-btn" onclick="dailySignIn()" id="signinBtn" data-i18n="daily-signin">每日签到</button>
            <button class="task-panel" onclick="showTaskModal()" data-i18n="my-tasks">我的任务</button>
            <button class="auth-btn" onclick="logout()" data-i18n="logout">退出</button>
        </div>
        <div id="authButtons">
            <button class="auth-btn" onclick="showAuthModal('login')" data-i18n="login">登录</button>
            <button class="auth-btn" onclick="showAuthModal('register')" data-i18n="register">注册</button>
        </div>
    </div>

    <div class="language-switcher">
        <button class="lang-btn active" onclick="switchLanguage('zh')" id="lang-zh">中文</button>
        <button class="lang-btn" onclick="switchLanguage('en')" id="lang-en">English</button>
    </div>

    <div class="container">
        <div class="header">
            <h1 data-i18n="main-title">双语书籍翻译服务</h1>
            <p data-i18n="main-subtitle">将您的书籍转换为双语版本，支持多种语言和翻译风格</p>
        </div>
        
        <div class="card">
            <form id="uploadForm">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📚</div>
                    <h3 data-i18n="upload-title">拖拽文件到此处或点击选择</h3>
                    <p data-i18n="upload-subtitle">支持 TXT、EPUB、PDF、Word、MOBI 等16种格式，最大 100MB</p>
                    <input type="file" id="fileInput" accept=".txt,.epub,.pdf,.docx,.doc,.mobi,.azw,.azw3,.fb2,.html,.htm,.rtf,.odt" style="display: none;">
                </div>

                <div class="file-info" id="fileInfo">
                    <strong data-i18n="selected-file">已选择文件：</strong>
                    <span id="fileName"></span>
                    <br>
                    <strong data-i18n="file-size">文件大小：</strong>
                    <span id="fileSize"></span>
                </div>

                <div class="form-group">
                    <label for="targetLanguage" data-i18n="target-language">目标语言</label>
                    <select id="targetLanguage" class="form-control">
                        <option value="中文">中文</option>
                        <option value="English">English</option>
                        <option value="日本語">日本語</option>
                        <option value="한국어">한국어</option>
                        <option value="Français">Français</option>
                        <option value="Deutsch">Deutsch</option>
                        <option value="Español">Español</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="translationStyle" data-i18n="translation-style">翻译风格</label>
                    <select id="translationStyle" class="form-control">
                        <option value="casual" data-i18n="style-casual">口语化 - 轻松自然的表达</option>
                        <option value="faithful" data-i18n="style-faithful">严谨忠实 - 保持原文结构</option>
                    </select>
                </div>

                <button type="submit" class="btn" id="translateBtn" data-i18n="start-translation">
                    开始翻译
                </button>
            </form>
            
            <div class="progress hidden" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="status" id="statusMessage"></div>
        </div>
    </div>

    <!-- 认证模态框 -->
    <div id="authModal" class="auth-modal">
        <div class="auth-modal-content">
            <span class="close" onclick="closeAuthModal()">&times;</span>

            <!-- 登录表单 -->
            <div id="loginForm" class="auth-form">
                <h2 data-i18n="login-title">用户登录</h2>
                <form onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <input type="email" id="loginUsername" class="form-control" placeholder="邮箱" data-i18n-placeholder="email-placeholder" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="loginPassword" class="form-control" placeholder="密码" data-i18n-placeholder="password-placeholder" required>
                    </div>
                    <button type="submit" class="btn" data-i18n="login">登录</button>
                </form>
                <div class="auth-switch">
                    <span data-i18n="no-account">还没有账号？</span>
                    <a onclick="switchToRegister()" data-i18n="register-now">立即注册</a>
                </div>
            </div>

            <!-- 注册表单 -->
            <div id="registerForm" class="auth-form" style="display: none;">
                <h2 data-i18n="register-title">用户注册</h2>
                <form onsubmit="handleRegister(event)">
                    <div class="form-group">
                        <input type="email" id="registerEmail" class="form-control" placeholder="邮箱" data-i18n-placeholder="email-placeholder" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="registerPassword" class="form-control" placeholder="密码" data-i18n-placeholder="password-placeholder" required>
                    </div>
                    <div class="form-group">
                        <input type="password" id="confirmPassword" class="form-control" placeholder="确认密码" data-i18n-placeholder="confirm-password-placeholder" required>
                    </div>
                    <button type="submit" class="btn" data-i18n="register">注册</button>
                </form>
                <div class="auth-switch">
                    <span data-i18n="have-account">已有账号？</span>
                    <a onclick="switchToLogin()" data-i18n="login-now">立即登录</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务管理模态框 -->
    <div id="taskModal" class="task-modal">
        <div class="task-modal-content">
            <span class="close" onclick="closeTaskModal()">&times;</span>

            <h2 data-i18n="task-management">任务管理</h2>

            <div class="tab-buttons">
                <button class="tab-btn active" onclick="switchTab('tasks')" data-i18n="my-tasks">我的任务</button>
                <button class="tab-btn" onclick="switchTab('points')" data-i18n="points-history">积分历史</button>
            </div>

            <!-- 任务列表标签页 -->
            <div id="tasksTab" class="tab-content active">
                <div class="task-list" id="taskList">
                    <div data-i18n="loading">加载中...</div>
                </div>
            </div>

            <!-- 积分历史标签页 -->
            <div id="pointsTab" class="tab-content">
                <div class="points-history" id="pointsHistory">
                    <div data-i18n="loading">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 多语言支持
        const translations = {
            zh: {
                'page-title': '双语书籍翻译服务',
                'main-title': '双语书籍翻译服务',
                'main-subtitle': '将您的书籍转换为双语版本，支持多种语言和翻译风格',
                'upload-title': '拖拽文件到此处或点击选择',
                'upload-subtitle': '支持 TXT、EPUB、PDF、Word、MOBI 等16种格式，最大 100MB',
                'selected-file': '已选择文件：',
                'file-size': '文件大小：',
                'target-language': '目标语言',
                'translation-style': '翻译风格',
                'style-casual': '口语化 - 轻松自然的表达',
                'style-faithful': '严谨忠实 - 保持原文结构',
                'start-translation': '开始翻译',
                'please-select-file': '请先选择文件',
                'uploading-file': '正在上传文件...',
                'translating': '正在翻译，请稍候...',
                'file-upload-failed': '文件上传失败',
                'translation-task-failed': '翻译任务创建失败',
                'query-status-failed': '查询任务状态失败',
                'translation-completed': '翻译完成！',
                'download-bilingual': '下载双语版本',
                'download-formats': '下载格式选择：',
                'translation-failed': '翻译失败',
                'translating-progress': '正在翻译中...',
                'translation-timeout': '翻译超时',
                'task-queued': '任务排队中...',
                'task-timeout': '任务超时',
                'error': '错误：',
                'welcome': '欢迎，',
                'points': '积分：',
                'daily-signin': '每日签到',
                'logout': '退出',
                'login': '登录',
                'register': '注册',
                'login-title': '用户登录',
                'register-title': '用户注册',
                'username-placeholder': '用户名',
                'password-placeholder': '密码',
                'email-placeholder': '邮箱',
                'confirm-password-placeholder': '确认密码',
                'no-account': '还没有账号？',
                'register-now': '立即注册',
                'have-account': '已有账号？',
                'login-now': '立即登录',
                'signin-success': '签到成功！获得10积分',
                'signin-already': '今日已签到',
                'login-success': '登录成功！',
                'register-success': '注册成功！',
                'password-mismatch': '两次输入的密码不一致',
                'login-failed': '登录失败',
                'register-failed': '注册失败',
                'please-login': '请先登录',
                'insufficient-points': '积分不足',
                'my-tasks': '我的任务',
                'task-management': '任务管理',
                'points-history': '积分历史',
                'loading': '加载中...',
                'task-pending': '等待中',
                'task-processing': '处理中',
                'task-completed': '已完成',
                'task-failed': '失败',
                'download': '下载',
                'view-details': '查看详情',
                'no-tasks': '暂无任务',
                'no-points-history': '暂无积分记录',
                'created-at': '创建时间',
                'status': '状态',
                'cost': '费用',
                'points-earned': '获得积分',
                'points-spent': '消费积分',
                'transaction-type': '交易类型',
                'description': '描述',
                'amount': '金额',
                'date': '日期'
            },
            en: {
                'page-title': 'Bilingual Book Translation Service',
                'main-title': 'Bilingual Book Translation Service',
                'main-subtitle': 'Convert your books to bilingual versions with support for multiple languages and translation styles',
                'upload-title': 'Drag files here or click to select',
                'upload-subtitle': 'Supports TXT, EPUB, PDF, Word, MOBI and 16 more formats, max 100MB',
                'selected-file': 'Selected file:',
                'file-size': 'File size:',
                'target-language': 'Target Language',
                'translation-style': 'Translation Style',
                'style-casual': 'Casual - Natural and relaxed expression',
                'style-faithful': 'Faithful - Maintain original structure',
                'start-translation': 'Start Translation',
                'please-select-file': 'Please select a file first',
                'uploading-file': 'Uploading file...',
                'translating': 'Translating, please wait...',
                'file-upload-failed': 'File upload failed',
                'translation-task-failed': 'Translation task creation failed',
                'query-status-failed': 'Query task status failed',
                'translation-completed': 'Translation completed!',
                'download-bilingual': 'Download Bilingual Version',
                'download-formats': 'Download Format Options:',
                'translation-failed': 'Translation failed',
                'translating-progress': 'Translating in progress...',
                'translation-timeout': 'Translation timeout',
                'task-queued': 'Task queued...',
                'task-timeout': 'Task timeout',
                'error': 'Error: ',
                'welcome': 'Welcome, ',
                'points': 'Points: ',
                'daily-signin': 'Daily Sign-in',
                'logout': 'Logout',
                'login': 'Login',
                'register': 'Register',
                'login-title': 'User Login',
                'register-title': 'User Registration',
                'username-placeholder': 'Username',
                'password-placeholder': 'Password',
                'email-placeholder': 'Email',
                'confirm-password-placeholder': 'Confirm Password',
                'no-account': "Don't have an account? ",
                'register-now': 'Register now',
                'have-account': 'Already have an account? ',
                'login-now': 'Login now',
                'signin-success': 'Sign-in successful! Earned 10 points',
                'signin-already': 'Already signed in today',
                'login-success': 'Login successful!',
                'register-success': 'Registration successful!',
                'password-mismatch': 'Passwords do not match',
                'login-failed': 'Login failed',
                'register-failed': 'Registration failed',
                'please-login': 'Please login first',
                'insufficient-points': 'Insufficient points',
                'my-tasks': 'My Tasks',
                'task-management': 'Task Management',
                'points-history': 'Points History',
                'loading': 'Loading...',
                'task-pending': 'Pending',
                'task-processing': 'Processing',
                'task-completed': 'Completed',
                'task-failed': 'Failed',
                'download': 'Download',
                'view-details': 'View Details',
                'no-tasks': 'No tasks yet',
                'no-points-history': 'No points history',
                'created-at': 'Created At',
                'status': 'Status',
                'cost': 'Cost',
                'points-earned': 'Points Earned',
                'points-spent': 'Points Spent',
                'transaction-type': 'Transaction Type',
                'description': 'Description',
                'amount': 'Amount',
                'date': 'Date'
            }
        };

        let currentLanguage = 'zh';
        let currentUser = null;

        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
        });

        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const token = localStorage.getItem('auth_token');
                if (!token) {
                    showAuthButtons();
                    return;
                }

                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const userData = await response.json();
                    currentUser = userData.user;
                    updateUserInterface(userData.user);
                } else {
                    localStorage.removeItem('auth_token');
                    showAuthButtons();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                localStorage.removeItem('auth_token');
                showAuthButtons();
            }
        }

        // 更新用户界面
        function updateUserInterface(userData) {
            document.getElementById('username').textContent = userData.email;
            document.getElementById('userPoints').textContent = userData.points_balance;
            document.getElementById('userInfo').classList.add('show');
            document.getElementById('authButtons').style.display = 'none';

            // 检查是否已签到
            if (userData.last_signin && userData.last_signin === new Date().toISOString().split('T')[0]) {
                document.getElementById('signinBtn').disabled = true;
                document.getElementById('signinBtn').textContent = t('signin-already');
            }
        }

        // 显示认证按钮
        function showAuthButtons() {
            document.getElementById('userInfo').classList.remove('show');
            document.getElementById('authButtons').style.display = 'flex';
        }

        // 显示认证模态框
        function showAuthModal(type) {
            document.getElementById('authModal').style.display = 'block';
            if (type === 'login') {
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('registerForm').style.display = 'none';
            } else {
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('registerForm').style.display = 'block';
            }
        }

        // 关闭认证模态框
        function closeAuthModal() {
            document.getElementById('authModal').style.display = 'none';
        }

        // 切换到注册表单
        function switchToRegister() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        // 切换到登录表单
        function switchToLogin() {
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
        }

        // 处理登录
        async function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const userData = await response.json();
                    localStorage.setItem('auth_token', userData.token);
                    currentUser = userData.user;
                    updateUserInterface(userData.user);
                    closeAuthModal();
                    showStatus('login-success', 'success');
                } else {
                    const error = await response.json();
                    showStatus('login-failed', 'error', t('login-failed') + ': ' + error.error);
                }
            } catch (error) {
                showStatus('login-failed', 'error', t('login-failed') + ': ' + error.message);
            }
        }

        // 处理注册
        async function handleRegister(event) {
            event.preventDefault();
            const email = document.getElementById('registerEmail').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (password !== confirmPassword) {
                showStatus('password-mismatch', 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });

                if (response.ok) {
                    const userData = await response.json();
                    localStorage.setItem('auth_token', userData.token);
                    currentUser = userData.user;
                    updateUserInterface(userData.user);
                    closeAuthModal();
                    showStatus('register-success', 'success');
                } else {
                    const error = await response.json();
                    showStatus('register-failed', 'error', t('register-failed') + ': ' + error.error);
                }
            } catch (error) {
                showStatus('register-failed', 'error', t('register-failed') + ': ' + error.message);
            }
        }

        // 退出登录
        async function logout() {
            try {
                localStorage.removeItem('auth_token');
                currentUser = null;
                showAuthButtons();
                showStatus('logout-success', 'success', '已退出登录');
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }

        // 每日签到
        async function dailySignIn() {
            if (!currentUser) {
                showStatus('please-login', 'error');
                return;
            }

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/auth/signin', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    document.getElementById('userPoints').textContent = result.points_balance;
                    document.getElementById('signinBtn').disabled = true;
                    document.getElementById('signinBtn').textContent = t('signin-already');
                    showStatus('signin-success', 'success');
                } else {
                    const error = await response.json();
                    showStatus('signin-already', 'info', error.message);
                }
            } catch (error) {
                showStatus('signin-failed', 'error', '签到失败: ' + error.message);
            }
        }

        // 显示任务管理模态框
        function showTaskModal() {
            if (!currentUser) {
                showStatus('please-login', 'error');
                return;
            }

            document.getElementById('taskModal').style.display = 'block';
            loadUserTasks();
        }

        // 关闭任务管理模态框
        function closeTaskModal() {
            document.getElementById('taskModal').style.display = 'none';
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 显示对应内容
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName + 'Tab').classList.add('active');

            // 加载对应数据
            if (tabName === 'tasks') {
                loadUserTasks();
            } else if (tabName === 'points') {
                loadPointsHistory();
            }
        }

        // 加载用户任务
        async function loadUserTasks() {
            const taskList = document.getElementById('taskList');
            taskList.innerHTML = '<div>' + t('loading') + '</div>';

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/user/jobs?limit=20', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayTasks(data.jobs);
                } else {
                    taskList.innerHTML = '<div>加载任务失败</div>';
                }
            } catch (error) {
                taskList.innerHTML = '<div>加载任务失败: ' + error.message + '</div>';
            }
        }

        // 显示任务列表
        function displayTasks(tasks) {
            const taskList = document.getElementById('taskList');

            if (tasks.length === 0) {
                taskList.innerHTML = '<div>' + t('no-tasks') + '</div>';
                return;
            }

            let html = '';
            tasks.forEach(task => {
                const statusClass = task.status;
                const statusText = t('task-' + task.status);
                const createdAt = new Date(task.created_at).toLocaleString();

                html += `
                    <div class="task-item">
                        <div class="task-header">
                            <div class="task-title">${task.original_filename}</div>
                            <div class="task-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="task-details">
                            <div><strong>${t('created-at')}:</strong> ${createdAt}</div>
                            <div><strong>${t('target-language')}:</strong> ${task.target_language}</div>
                            <div><strong>${t('translation-style')}:</strong> ${task.style}</div>
                            <div><strong>${t('cost')}:</strong> ${task.cost_points} ${t('points')}</div>
                        </div>
                        <div class="task-actions">
                            ${task.status === 'completed' ? generateDownloadButtons(task) : ''}
                        </div>
                    </div>
                `;
            });

            taskList.innerHTML = html;
        }

        // 生成下载按钮
        function generateDownloadButtons(task) {
            let buttons = '';
            if (task.output_files) {
                Object.keys(task.output_files).forEach(format => {
                    buttons += `<a href="/api/download/${task.job_id}/${format}" class="btn" target="_blank">${t('download')} ${format.toUpperCase()}</a>`;
                });
            }
            return buttons;
        }

        // 加载积分历史
        async function loadPointsHistory() {
            const pointsHistory = document.getElementById('pointsHistory');
            pointsHistory.innerHTML = '<div>' + t('loading') + '</div>';

            try {
                const token = localStorage.getItem('auth_token');
                const response = await fetch('/api/user/points/history?limit=50', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayPointsHistory(data.transactions, data.current_balance);
                } else {
                    pointsHistory.innerHTML = '<div>加载积分历史失败</div>';
                }
            } catch (error) {
                pointsHistory.innerHTML = '<div>加载积分历史失败: ' + error.message + '</div>';
            }
        }

        // 显示积分历史
        function displayPointsHistory(transactions, currentBalance) {
            const pointsHistory = document.getElementById('pointsHistory');

            if (transactions.length === 0) {
                pointsHistory.innerHTML = '<div>' + t('no-points-history') + '</div>';
                return;
            }

            let html = `<div style="margin-bottom: 20px; font-weight: bold;">当前余额: ${currentBalance} ${t('points')}</div>`;

            transactions.forEach(transaction => {
                const isPositive = transaction.amount > 0;
                const amountClass = isPositive ? 'positive' : 'negative';
                const amountText = (isPositive ? '+' : '') + transaction.amount;
                const date = new Date(transaction.created_at).toLocaleString();

                html += `
                    <div class="points-item">
                        <div>
                            <div><strong>${transaction.description}</strong></div>
                            <div style="font-size: 12px; color: #666;">${date}</div>
                        </div>
                        <div class="points-amount ${amountClass}">${amountText}</div>
                    </div>
                `;
            });

            pointsHistory.innerHTML = html;
        }

        function switchLanguage(lang) {
            currentLanguage = lang;

            // 更新按钮状态
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`lang-${lang}`).classList.add('active');

            // 更新页面文本
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (translations[lang] && translations[lang][key]) {
                    if (element.tagName === 'OPTION') {
                        element.textContent = translations[lang][key];
                    } else {
                        element.textContent = translations[lang][key];
                    }
                }
            });

            // 更新placeholder文本
            document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
                const key = element.getAttribute('data-i18n-placeholder');
                if (translations[lang] && translations[lang][key]) {
                    element.placeholder = translations[lang][key];
                }
            });

            // 更新页面标题
            document.title = translations[lang]['page-title'];

            // 更新HTML lang属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
        }

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadForm = document.getElementById('uploadForm');
        const translateBtn = document.getElementById('translateBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const statusMessage = document.getElementById('statusMessage');

        let selectedFile = null;
        let uploadId = null;
        
        // 文件拖拽处理
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
        
        function handleFileSelect(file) {
            selectedFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function showStatus(messageKey, type, customMessage = null) {
            const message = customMessage || translations[currentLanguage][messageKey] || messageKey;
            statusMessage.textContent = message;
            statusMessage.className = `status ${type}`;
            statusMessage.style.display = 'block';
        }

        function t(key) {
            return translations[currentLanguage][key] || key;
        }
        
        function hideStatus() {
            statusMessage.style.display = 'none';
        }
        
        function setProgress(percent) {
            progressBar.style.width = percent + '%';
        }
        
        function showProgress() {
            progressContainer.classList.remove('hidden');
        }
        
        function hideProgress() {
            progressContainer.classList.add('hidden');
        }
        
        // 表单提交处理
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (!currentUser) {
                showStatus('please-login', 'error');
                return;
            }

            if (!selectedFile) {
                showStatus('please-select-file', 'error');
                return;
            }

            // 检查积分是否足够（假设翻译需要50积分）
            if (currentUser.points < 50) {
                showStatus('insufficient-points', 'error');
                return;
            }

            translateBtn.disabled = true;
            hideStatus();
            showProgress();
            setProgress(10);

            try {
                // 上传文件
                showStatus('uploading-file', 'info');
                const formData = new FormData();
                formData.append('file', selectedFile);

                const uploadResponse = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!uploadResponse.ok) {
                    throw new Error(t('file-upload-failed'));
                }

                const uploadResult = await uploadResponse.json();
                uploadId = uploadResult.upload_id;
                setProgress(30);

                // 开始翻译
                showStatus('translating', 'info');
                const translateData = {
                    upload_id: uploadId,
                    target_language: document.getElementById('targetLanguage').value,
                    style: document.getElementById('translationStyle').value
                };

                const token = localStorage.getItem('auth_token');
                const translateResponse = await fetch('/api/translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(translateData)
                });

                if (!translateResponse.ok) {
                    throw new Error(t('translation-task-failed'));
                }

                const translateResult = await translateResponse.json();
                const jobId = translateResult.job_id;
                setProgress(50);

                // 轮询任务状态
                await pollJobStatus(jobId);

            } catch (error) {
                showStatus('error', 'error', t('error') + error.message);
                hideProgress();
                translateBtn.disabled = false;
            }
        });
        
        async function pollJobStatus(jobId) {
            const maxAttempts = 60; // 最多等待5分钟
            let attempts = 0;

            const poll = async () => {
                try {
                    const response = await fetch(`/api/job/${jobId}`);
                    if (!response.ok) {
                        throw new Error(t('query-status-failed'));
                    }

                    const job = await response.json();

                    if (job.status === 'completed') {
                        setProgress(100);
                        showStatus('translation-completed', 'success');

                        // 获取下载信息并添加多格式下载链接
                        fetch(`/api/download-info/${jobId}`)
                            .then(response => response.json())
                            .then(downloadInfo => {
                                if (downloadInfo.formats) {
                                    const downloadContainer = document.createElement('div');
                                    downloadContainer.className = 'download-container';
                                    downloadContainer.style.marginTop = '15px';

                                    const title = document.createElement('h4');
                                    title.textContent = t('download-formats');
                                    title.style.margin = '10px 0';
                                    downloadContainer.appendChild(title);

                                    // 为每种格式创建下载按钮
                                    Object.entries(downloadInfo.formats).forEach(([format, info]) => {
                                        if (info.available) {
                                            const downloadBtn = document.createElement('a');
                                            downloadBtn.href = info.download_url;
                                            downloadBtn.className = 'download-btn';
                                            downloadBtn.textContent = `${format.toUpperCase()} (${info.size_mb}MB)`;
                                            downloadBtn.style.cssText = `
                                                display: inline-block;
                                                margin: 5px 10px 5px 0;
                                                padding: 8px 16px;
                                                background: #007bff;
                                                color: white;
                                                text-decoration: none;
                                                border-radius: 4px;
                                                font-size: 14px;
                                            `;
                                            downloadContainer.appendChild(downloadBtn);
                                        }
                                    });

                                    statusMessage.appendChild(downloadContainer);
                                }
                            })
                            .catch(error => {
                                console.error('获取下载信息失败:', error);
                                // 降级到单一下载链接
                                const downloadLink = document.createElement('a');
                                downloadLink.href = `/api/download/${jobId}`;
                                downloadLink.className = 'download-link';
                                downloadLink.textContent = t('download-bilingual');
                                statusMessage.appendChild(downloadLink);
                            });

                        translateBtn.disabled = false;

                    } else if (job.status === 'failed') {
                        throw new Error(job.error_message || t('translation-failed'));

                    } else if (job.status === 'processing') {
                        setProgress(50 + (attempts / maxAttempts) * 40);
                        showStatus('translating-progress', 'info');

                        attempts++;
                        if (attempts < maxAttempts) {
                            setTimeout(poll, 5000); // 5秒后重试
                        } else {
                            throw new Error(t('translation-timeout'));
                        }

                    } else {
                        // pending状态
                        setProgress(40);
                        showStatus('task-queued', 'info');

                        attempts++;
                        if (attempts < maxAttempts) {
                            setTimeout(poll, 3000); // 3秒后重试
                        } else {
                            throw new Error(t('task-timeout'));
                        }
                    }

                } catch (error) {
                    showStatus('error', 'error', t('error') + error.message);
                    hideProgress();
                    translateBtn.disabled = false;
                }
            };

            poll();
        }
    </script>
</body>
</html>
