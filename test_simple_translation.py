#!/usr/bin/env python3
"""
简单的翻译功能测试
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:5000"

def test_simple_translation():
    """测试简单的翻译功能"""
    print("🔧 简单翻译功能测试")
    print("=" * 50)
    
    # 1. 创建简单的TXT测试文件
    print("1️⃣ 创建测试文件...")
    test_content = """Hello, this is a test document.

This document contains a few paragraphs for testing the translation functionality.

The first paragraph talks about testing software.

The second paragraph discusses automated testing.

This is the final paragraph."""
    
    test_file_path = "simple_test.txt"
    try:
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"   ✅ 创建测试文件: {test_file_path}")
    except Exception as e:
        print(f"   ❌ 创建文件失败: {e}")
        return
    
    # 2. 测试上传
    print("\n2️⃣ 测试文件上传...")
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path, f, 'text/plain')}
            upload_response = requests.post(f"{BASE_URL}/api/upload", files=files)
        
        print(f"   状态码: {upload_response.status_code}")
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            upload_id = upload_result['upload_id']
            print("   ✅ 文件上传成功")
            print(f"   📁 Upload ID: {upload_id}")
            print(f"   📊 文件大小: {upload_result['file_size']} bytes")
        else:
            print(f"   ❌ 上传失败: {upload_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return
    
    # 3. 测试翻译（不需要认证的版本）
    print("\n3️⃣ 测试翻译功能...")
    translate_data = {
        "upload_id": upload_id,
        "target_language": "中文",
        "style": "casual"
    }
    
    try:
        print("   发送翻译请求...")
        translate_response = requests.post(
            f"{BASE_URL}/api/translate",
            json=translate_data,
            headers={'Content-Type': 'application/json'},
            timeout=30  # 30秒超时
        )
        
        print(f"   状态码: {translate_response.status_code}")
        print(f"   响应内容: {translate_response.text[:500]}...")
        
        if translate_response.status_code == 200:
            translate_result = translate_response.json()
            print("   ✅ 翻译请求成功")
            print(f"   📋 任务ID: {translate_result.get('job_id', 'N/A')}")
            print(f"   📊 状态: {translate_result.get('status', 'N/A')}")
        elif translate_response.status_code == 401:
            print("   ⚠️  需要登录认证")
        else:
            print(f"   ❌ 翻译失败")
            
    except requests.exceptions.Timeout:
        print("   ⏰ 翻译请求超时")
    except Exception as e:
        print(f"   ❌ 翻译异常: {e}")
    
    # 清理测试文件
    try:
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
            print(f"\n🧹 清理测试文件: {test_file_path}")
    except:
        pass
    
    print("\n✅ 简单翻译测试完成！")

def test_server_connection():
    """测试服务器连接"""
    print("🌐 测试服务器连接")
    print("=" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"主页状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服务器连接正常")
        else:
            print("❌ 服务器响应异常")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")

if __name__ == "__main__":
    test_server_connection()
    print()
    test_simple_translation()
