#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多格式支持功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.loader.multi_format_loader import MultiFormatLoader, get_file_info
from src.generator.multi_format_generator import MultiFormatGenerator
from src.converter.format_converter import FormatConverter

def test_format_converter():
    """测试格式转换器"""
    print("🔧 测试格式转换器...")
    
    converter = FormatConverter()
    
    print(f"Calibre可用: {converter.calibre_available}")
    print(f"支持的输入格式: {list(converter.SUPPORTED_INPUT_FORMATS)}")
    print(f"支持的输出格式: {list(converter.SUPPORTED_OUTPUT_FORMATS)}")
    
    # 测试格式检测
    test_files = [
        "test.txt", "test.epub", "test.pdf", "test.docx", 
        "test.mobi", "test.html", "test.xyz"
    ]
    
    for file_path in test_files:
        supported = converter.is_supported_input(file_path)
        needs_conversion = converter.needs_conversion(file_path)
        print(f"  {file_path}: 支持={supported}, 需要转换={needs_conversion}")

def test_multi_format_loader():
    """测试多格式加载器"""
    print("\n📚 测试多格式加载器...")
    
    loader = MultiFormatLoader()
    
    print(f"支持的格式: {loader.get_supported_formats()}")
    
    # 测试现有的TXT文件
    test_file = "test_sample_en.txt"
    if os.path.exists(test_file):
        try:
            print(f"测试加载文件: {test_file}")
            file_info = get_file_info(test_file)
            print(f"文件信息: {file_info}")
            
            chapters, original_format = loader.load_file(test_file)
            print(f"加载成功: {len(chapters)} 个章节, 原始格式: {original_format}")
            print(f"第一章节预览: {chapters[0][:100]}...")
            
        except Exception as e:
            print(f"加载失败: {str(e)}")
        finally:
            loader.cleanup()
    else:
        print(f"测试文件不存在: {test_file}")

def test_multi_format_generator():
    """测试多格式生成器"""
    print("\n📄 测试多格式生成器...")
    
    generator = MultiFormatGenerator()
    
    print(f"支持的输出格式: {generator.get_supported_output_formats()}")
    
    # 创建测试内容
    original_chapters = [
        "Chapter 1: Introduction\n\nThis is the first chapter of our test book.",
        "Chapter 2: Content\n\nThis chapter contains the main content.",
        "Chapter 3: Conclusion\n\nThis is the final chapter."
    ]
    
    translated_chapters = [
        "第一章：介绍\n\n这是我们测试书籍的第一章。",
        "第二章：内容\n\n本章包含主要内容。", 
        "第三章：结论\n\n这是最后一章。"
    ]
    
    # 测试文件大小估算
    content_size = sum(len(chapter.encode('utf-8')) for chapter in original_chapters)
    estimates = generator.estimate_file_sizes(content_size)
    print(f"文件大小估算: {estimates}")
    
    # 如果Calibre可用，测试生成功能
    if generator.converter.calibre_available:
        try:
            output_dir = "test_output"
            os.makedirs(output_dir, exist_ok=True)
            
            print("测试生成EPUB格式...")
            epub_success = generator.generate_single_format(
                original_chapters, translated_chapters,
                os.path.join(output_dir, "test_bilingual.epub"),
                "epub"
            )
            print(f"EPUB生成: {'成功' if epub_success else '失败'}")
            
        except Exception as e:
            print(f"生成测试失败: {str(e)}")
        finally:
            generator.cleanup()
    else:
        print("Calibre不可用，跳过生成测试")

def test_format_info():
    """测试格式信息"""
    print("\n📋 测试格式信息...")
    
    from src.generator.multi_format_generator import get_format_info
    
    format_info = get_format_info()
    for fmt, info in format_info.items():
        print(f"  {fmt}: {info['name']} - {info['description']}")

def main():
    """主测试函数"""
    print("🚀 多格式支持功能测试")
    print("=" * 50)
    
    test_format_converter()
    test_multi_format_loader()
    test_multi_format_generator()
    test_format_info()
    
    print("\n✅ 测试完成！")
    
    # 提供Calibre安装指南
    converter = FormatConverter()
    if not converter.calibre_available:
        print("\n⚠️  注意：Calibre未安装或不可用")
        print("为了完整支持多格式转换，请安装Calibre:")
        print("https://calibre-ebook.com/download")

if __name__ == "__main__":
    main()
