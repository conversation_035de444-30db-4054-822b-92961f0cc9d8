#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的多格式生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.generator.multi_format_generator import MultiFormatGenerator

def test_fixed_formats():
    """测试修复后的格式生成"""
    print("🚀 测试修复后的多格式生成...")
    
    # 测试数据 - 包含中文
    original_chapters = [
        "The Art of Programming",
        "Programming is both an art and a science. It requires creativity, logical thinking, and attention to detail."
    ]
    
    translated_chapters = [
        "编程的艺术",
        "编程既是艺术也是科学。它需要创造力、逻辑思维和对细节的关注。"
    ]
    
    # 创建输出目录
    output_dir = "test_fixed_output"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 创建生成器
        generator = MultiFormatGenerator()
        
        print("📚 测试单独格式生成...")
        
        # 测试TXT格式
        print("📝 测试TXT格式...")
        success = generator._generate_txt_format(
            original_chapters, translated_chapters, f"{output_dir}/test_fixed.txt"
        )
        print(f"   TXT生成: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试DOCX格式
        print("📄 测试DOCX格式...")
        success = generator._generate_docx_format(
            original_chapters, translated_chapters, f"{output_dir}/test_fixed.docx"
        )
        print(f"   DOCX生成: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试PDF格式
        print("📋 测试PDF格式...")
        success = generator._generate_pdf_format(
            original_chapters, translated_chapters, f"{output_dir}/test_fixed.pdf"
        )
        print(f"   PDF生成: {'✅ 成功' if success else '❌ 失败'}")
        
        # 检查生成的文件
        print("\n📊 检查生成的文件...")
        for fmt in ['txt', 'docx', 'pdf']:
            file_path = f"{output_dir}/test_fixed.{fmt}"
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   📄 {fmt.upper()}: {file_path} ({size} bytes)")
            else:
                print(f"   ❌ {fmt.upper()}: 文件不存在")
        
        # 检查TXT文件内容
        txt_file = f"{output_dir}/test_fixed.txt"
        if os.path.exists(txt_file):
            print("\n📝 TXT文件内容预览:")
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"   内容长度: {len(content)} 字符")
                print(f"   前200字符: {content[:200]}...")
                
                # 检查是否包含标签
                if "原文:" in content or "译文:" in content:
                    print("   ⚠️  警告: 发现标签文字")
                else:
                    print("   ✅ 无标签文字")
        
        # 清理
        generator.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🚀 修复后的多格式生成测试")
    print("=" * 50)
    
    test_fixed_formats()
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
