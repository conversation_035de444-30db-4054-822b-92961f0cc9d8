#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试格式修复
验证EPUB不自动分章节，PDF不自动缩进
"""

import os
import sys
import tempfile
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.generator.multi_format_generator import MultiFormatGenerator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_epub_format_fix():
    """测试EPUB格式修复 - 不自动分章节"""
    print("\n📚 测试EPUB格式修复")
    print("=" * 50)
    
    try:
        # 创建测试内容
        original_chapters = [
            "Programming is both an art and a science. It requires creativity, logical thinking, and attention to detail.",
            "The first step in programming is understanding the problem you want to solve.",
            "Good code is not just functional, but also readable and maintainable."
        ]
        
        translated_chapters = [
            "编程既是一门艺术，也是一门科学。它需要创造力、逻辑思维和对细节的关注。",
            "编程的第一步是理解你想要解决的问题。",
            "好的代码不仅仅是功能性的，还应该是可读和可维护的。"
        ]
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = os.path.join(temp_dir, "test_output")
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建多格式生成器
            generator = MultiFormatGenerator(temp_dir)
            
            # 生成EPUB格式
            epub_path = os.path.join(output_dir, "test_bilingual.epub")
            success = generator.generate_single_format(
                original_chapters, translated_chapters, epub_path, 'epub'
            )
            
            if success and os.path.exists(epub_path):
                logger.info("✅ EPUB文件生成成功")
                
                # 检查文件大小（简单验证）
                file_size = os.path.getsize(epub_path)
                logger.info(f"EPUB文件大小: {file_size} 字节")
                
                if file_size > 1000:  # 基本的大小检查
                    logger.info("✅ EPUB文件内容正常")
                    result = True
                else:
                    logger.error("❌ EPUB文件可能为空或损坏")
                    result = False
            else:
                logger.error("❌ EPUB文件生成失败")
                result = False
        
        print("✅ EPUB格式修复测试通过" if result else "❌ EPUB格式修复测试失败")
        return result
        
    except Exception as e:
        logger.error(f"EPUB格式修复测试失败: {str(e)}")
        print("❌ EPUB格式修复测试失败")
        return False

def test_pdf_format_fix():
    """测试PDF格式修复 - 译文不自动缩进"""
    print("\n📄 测试PDF格式修复")
    print("=" * 50)
    
    try:
        # 创建测试内容
        original_chapters = [
            "The Art of Programming",
            "Programming is both an art and a science. It requires creativity, logical thinking, and attention to detail."
        ]
        
        translated_chapters = [
            "编程的艺术",
            "编程既是一门艺术，也是一门科学。它需要创造力、逻辑思维和对细节的关注。"
        ]
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = os.path.join(temp_dir, "test_output")
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建多格式生成器
            generator = MultiFormatGenerator(temp_dir)
            
            # 生成PDF格式
            pdf_path = os.path.join(output_dir, "test_bilingual.pdf")
            success = generator.generate_single_format(
                original_chapters, translated_chapters, pdf_path, 'pdf'
            )
            
            if success and os.path.exists(pdf_path):
                logger.info("✅ PDF文件生成成功")
                
                # 检查文件大小
                file_size = os.path.getsize(pdf_path)
                logger.info(f"PDF文件大小: {file_size} 字节")
                
                if file_size > 5000:  # PDF文件通常较大
                    logger.info("✅ PDF文件内容正常")
                    result = True
                else:
                    logger.error("❌ PDF文件可能为空或损坏")
                    result = False
            else:
                logger.error("❌ PDF文件生成失败")
                result = False
        
        print("✅ PDF格式修复测试通过" if result else "❌ PDF格式修复测试失败")
        return result
        
    except Exception as e:
        logger.error(f"PDF格式修复测试失败: {str(e)}")
        print("❌ PDF格式修复测试失败")
        return False

def test_docx_format_consistency():
    """测试DOCX格式一致性"""
    print("\n📝 测试DOCX格式一致性")
    print("=" * 50)
    
    try:
        # 创建测试内容
        original_chapters = [
            "The Art of Programming",
            "Programming is both an art and a science. It requires creativity, logical thinking, and attention to detail."
        ]
        
        translated_chapters = [
            "编程的艺术",
            "编程既是一门艺术，也是一门科学。它需要创造力、逻辑思维和对细节的关注。"
        ]
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = os.path.join(temp_dir, "test_output")
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建多格式生成器
            generator = MultiFormatGenerator(temp_dir)
            
            # 生成DOCX格式
            docx_path = os.path.join(output_dir, "test_bilingual.docx")
            success = generator.generate_single_format(
                original_chapters, translated_chapters, docx_path, 'docx'
            )
            
            if success and os.path.exists(docx_path):
                logger.info("✅ DOCX文件生成成功")
                
                # 检查文件大小
                file_size = os.path.getsize(docx_path)
                logger.info(f"DOCX文件大小: {file_size} 字节")
                
                if file_size > 3000:  # DOCX文件基本大小
                    logger.info("✅ DOCX文件内容正常")
                    result = True
                else:
                    logger.error("❌ DOCX文件可能为空或损坏")
                    result = False
            else:
                logger.error("❌ DOCX文件生成失败")
                result = False
        
        print("✅ DOCX格式一致性测试通过" if result else "❌ DOCX格式一致性测试失败")
        return result
        
    except Exception as e:
        logger.error(f"DOCX格式一致性测试失败: {str(e)}")
        print("❌ DOCX格式一致性测试失败")
        return False

def test_all_formats_generation():
    """测试所有格式同时生成"""
    print("\n🎯 测试所有格式同时生成")
    print("=" * 50)
    
    try:
        # 创建测试内容
        original_chapters = [
            "The Art of Programming",
            "Programming is both an art and a science.",
            "Good code is readable and maintainable."
        ]
        
        translated_chapters = [
            "编程的艺术",
            "编程既是一门艺术，也是一门科学。",
            "好的代码是可读和可维护的。"
        ]
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            output_dir = os.path.join(temp_dir, "test_output")
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建多格式生成器
            generator = MultiFormatGenerator(temp_dir)
            
            # 生成所有格式
            results = generator.generate_all_formats(
                original_chapters=original_chapters,
                translated_chapters=translated_chapters,
                output_dir=output_dir,
                base_filename="test_bilingual",
                target_formats=['epub', 'pdf', 'docx']
            )
            
            logger.info(f"生成结果: {results}")
            
            # 检查所有格式是否生成成功
            expected_formats = ['epub', 'pdf', 'docx']
            success_count = 0
            
            for fmt in expected_formats:
                if fmt in results and os.path.exists(results[fmt]):
                    file_size = os.path.getsize(results[fmt])
                    logger.info(f"✅ {fmt.upper()}文件生成成功: {file_size} 字节")
                    success_count += 1
                else:
                    logger.error(f"❌ {fmt.upper()}文件生成失败")
            
            result = success_count == len(expected_formats)
        
        print("✅ 所有格式生成测试通过" if result else "❌ 所有格式生成测试失败")
        return result
        
    except Exception as e:
        logger.error(f"所有格式生成测试失败: {str(e)}")
        print("❌ 所有格式生成测试失败")
        return False

def main():
    """主测试函数"""
    print("🔧 格式修复验证测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("EPUB格式修复", test_epub_format_fix),
        ("PDF格式修复", test_pdf_format_fix),
        ("DOCX格式一致性", test_docx_format_consistency),
        ("所有格式生成", test_all_formats_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！格式问题已修复")
        print("📚 EPUB: 不再自动分章节，按原文格式显示")
        print("📄 PDF: 译文不再自动缩进，与原文对齐")
        print("📝 DOCX: 保持完美格式")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
