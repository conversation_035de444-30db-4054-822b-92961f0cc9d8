#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

if __name__ == '__main__':
    from app import app
    
    # 检查必要的环境变量
    if not os.environ.get('GEMINI_API_KEY'):
        print("警告: 未设置 GEMINI_API_KEY 环境变量")
        print("请复制 .env.example 为 .env 并填入正确的配置")
    
    # 启动应用
    app.run(
        debug=os.environ.get('FLASK_ENV') == 'development',
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000))
    )
