import os
import subprocess
import shutil

# 定义要处理的目录
input_directory = '/home/<USER>/ebook/in/'
output_directory = '/home/<USER>/ebook/in/temp/'
finish_directory = '/home/<USER>/ebook/in2/'

# 检查输出目录是否存在，如果不存在则创建
if not os.path.exists(output_directory):
    os.makedirs(output_directory)

# 遍历目录中的所有文件和子目录
for filename in os.listdir(output_directory):
    file_path = os.path.join(output_directory, filename)
    # 如果是文件，删除文件
    if os.path.isfile(file_path) or os.path.islink(file_path):
        os.unlink(file_path)
    # 如果是目录，删除目录及其内容
    elif os.path.isdir(file_path):
        shutil.rmtree(file_path)

# 初始化一个列表来存储所有EPUB文件的路径
epub_files = []

# 递归遍历目录，获取所有EPUB文件的路径
for root, dirs, files in os.walk(input_directory):
    for file in files:
        if file.endswith('.epub'):
            epub_files.append(os.path.join(root, file))

# 遍历所有EPUB文件
for epub_file in epub_files:
    # 构造输出文件路径
    mobi_output_file = os.path.join(output_directory, os.path.basename(epub_file.replace('.epub', '.mobi')))
    epub_output_file = os.path.join(finish_directory, os.path.basename(epub_file))

    # 使用ebook-convert命令将EPUB文件转换为MOBI格式
    ebook_convert_command = f'ebook-convert "{epub_file}" "{mobi_output_file}"'
    subprocess.run(ebook_convert_command, shell=True)
    
    # 将原始的EPUB文件移动到输出目录中
    shutil.move(epub_file, os.path.join(output_directory, os.path.basename(epub_file)))

    # 使用ebook-convert命令将MOBI文件转换回EPUB格式
    ebook_convert_command = f'ebook-convert "{mobi_output_file}" "{epub_output_file}"'
    subprocess.run(ebook_convert_command, shell=True)

    # 将转换后的EPUB文件复制到目录‘/home/<USER>/fastread/in/’
    shutil.copy(epub_output_file, '/home/<USER>/fastread/in/')

print('所有EPUB文件已转换为MOBI并再转换为EPUB格式')
