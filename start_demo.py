#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示启动脚本
"""

import os
import sys
from dotenv import load_dotenv

def check_environment():
    """检查环境配置"""
    print("检查环境配置...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要的环境变量
    gemini_key = os.environ.get('GEMINI_API_KEY')
    if not gemini_key or gemini_key == 'your_gemini_api_key_here':
        print("❌ 未配置 GEMINI_API_KEY")
        print("请编辑 .env 文件，填入你的 Gemini API 密钥")
        print("获取密钥：https://makersuite.google.com/app/apikey")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'requests',
        'beautifulsoup4',
        'tqdm',
        'rich',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def start_application():
    """启动应用"""
    print("启动应用...")
    
    try:
        from app import app
        
        print("🚀 应用启动成功！")
        print("📱 访问地址: http://localhost:5000")
        print("📝 上传测试文件: test_sample.txt")
        print("⏹️  按 Ctrl+C 停止服务")
        print("-" * 50)
        
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # 避免重复启动
        )
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🌟 雙語書籍翻譯服務 - MVP演示")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    print()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    print()
    
    # 启动应用
    start_application()

if __name__ == '__main__':
    main()
