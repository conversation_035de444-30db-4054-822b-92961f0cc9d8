#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能 - 后台翻译和积分系统
"""

import os
import sys
import json
import time
import uuid
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.user import User
from src.models.translation_job import TranslationJob
from src.models.points_transaction import PointsTransaction
from src.utils.task_queue import TaskQueue, add_translation_job
from src.utils.auth_utils import deduct_points_for_translation

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_points_system():
    """测试积分系统"""
    print("\n🧮 测试积分系统")
    print("=" * 50)
    
    try:
        # 创建测试用户
        test_email = "<EMAIL>"
        test_user = User.create_user(test_email, "password123")
        logger.info(f"创建测试用户: {test_email}")
        logger.info(f"初始积分: {test_user.points_balance}")
        
        # 测试签到
        signin_success = test_user.daily_signin()
        logger.info(f"签到结果: {'成功' if signin_success else '失败'}")
        logger.info(f"签到后积分: {test_user.points_balance}")
        
        # 测试重复签到
        signin_again = test_user.daily_signin()
        logger.info(f"重复签到结果: {'成功' if signin_again else '失败'}")
        
        # 测试翻译扣费
        job_id = str(uuid.uuid4())
        success, deducted, error = deduct_points_for_translation(test_user, job_id)
        logger.info(f"翻译扣费结果: {'成功' if success else '失败'}")
        logger.info(f"扣除积分: {deducted}")
        logger.info(f"扣费后积分: {test_user.points_balance}")
        
        # 测试积分历史
        transactions = PointsTransaction.get_user_transactions(test_user.user_id)
        logger.info(f"积分交易记录数: {len(transactions)}")
        for t in transactions:
            logger.info(f"  - {t.description}: {t.amount} 积分")
        
        # 清理测试数据
        os.remove(f'data/users/{test_user.user_id}.json')
        for t in transactions:
            try:
                os.remove(f'data/transactions/{t.transaction_id}.json')
            except:
                pass
        
        print("✅ 积分系统测试通过")
        return True
        
    except Exception as e:
        logger.error(f"积分系统测试失败: {str(e)}")
        print("❌ 积分系统测试失败")
        return False

def test_translation_job():
    """测试翻译任务模型"""
    print("\n📋 测试翻译任务模型")
    print("=" * 50)
    
    try:
        # 创建测试任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test.txt",
            file_path="/path/to/test.txt",
            target_language="中文",
            style="casual"
        )
        job.cost_points = 50
        job.user_id = "test-user-id"
        
        # 保存任务
        job.save()
        logger.info(f"创建测试任务: {job_id}")
        
        # 加载任务
        loaded_job = TranslationJob.load(job_id)
        if loaded_job:
            logger.info(f"任务加载成功: {loaded_job.job_id}")
            logger.info(f"任务状态: {loaded_job.status}")
            logger.info(f"积分费用: {loaded_job.cost_points}")
        else:
            raise Exception("任务加载失败")
        
        # 测试积分计算
        cost = job.calculate_cost()
        logger.info(f"翻译成本计算: {cost} 积分")
        
        # 清理测试数据
        os.remove(f'data/jobs/{job_id}.json')
        
        print("✅ 翻译任务模型测试通过")
        return True
        
    except Exception as e:
        logger.error(f"翻译任务模型测试失败: {str(e)}")
        print("❌ 翻译任务模型测试失败")
        return False

def test_task_queue():
    """测试任务队列"""
    print("\n⚙️ 测试任务队列")
    print("=" * 50)
    
    try:
        # 创建任务队列实例
        queue = TaskQueue()
        logger.info("创建任务队列实例")
        
        # 创建测试任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test.txt",
            file_path="test_sample.txt",  # 使用实际存在的测试文件
            target_language="中文",
            style="casual",
            status="pending"
        )
        job.save()
        logger.info(f"创建测试任务: {job_id}")
        
        # 测试添加任务到队列
        queue.add_job(job_id)
        logger.info("任务已添加到队列")
        
        # 清理测试数据
        try:
            os.remove(f'data/jobs/{job_id}.json')
        except:
            pass
        
        print("✅ 任务队列测试通过")
        return True
        
    except Exception as e:
        logger.error(f"任务队列测试失败: {str(e)}")
        print("❌ 任务队列测试失败")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n📁 测试目录结构")
    print("=" * 50)
    
    required_dirs = [
        'data',
        'data/users',
        'data/jobs',
        'data/transactions',
        'uploads',
        'outputs'
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            logger.info(f"✅ 目录存在: {dir_path}")
        else:
            logger.warning(f"❌ 目录不存在: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"✅ 已创建目录: {dir_path}")
    
    print("✅ 目录结构检查完成")
    return True

def main():
    """主测试函数"""
    print("🧪 新功能测试套件")
    print("=" * 60)
    
    # 确保目录存在
    test_directory_structure()
    
    # 运行测试
    tests = [
        ("积分系统", test_points_system),
        ("翻译任务模型", test_translation_job),
        ("任务队列", test_task_queue),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新功能已就绪")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
