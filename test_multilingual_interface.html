<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多语言界面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .lang-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .lang-btn.active {
            background: #28a745;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>多语言界面功能测试</h1>
    
    <div class="test-section">
        <h2>语言切换测试</h2>
        <button class="lang-btn active" onclick="switchLanguage('zh')" id="lang-zh">中文</button>
        <button class="lang-btn" onclick="switchLanguage('en')" id="lang-en">English</button>
    </div>
    
    <div class="test-section">
        <h2>界面文本测试</h2>
        <div class="test-item">
            <strong>页面标题：</strong>
            <span data-i18n="page-title">双语书籍翻译服务</span>
        </div>
        <div class="test-item">
            <strong>主标题：</strong>
            <span data-i18n="main-title">双语书籍翻译服务</span>
        </div>
        <div class="test-item">
            <strong>副标题：</strong>
            <span data-i18n="main-subtitle">将您的书籍转换为双语版本，支持多种语言和翻译风格</span>
        </div>
        <div class="test-item">
            <strong>上传标题：</strong>
            <span data-i18n="upload-title">拖拽文件到此处或点击选择</span>
        </div>
        <div class="test-item">
            <strong>目标语言：</strong>
            <span data-i18n="target-language">目标语言</span>
        </div>
        <div class="test-item">
            <strong>翻译风格：</strong>
            <span data-i18n="translation-style">翻译风格</span>
        </div>
        <div class="test-item">
            <strong>开始翻译：</strong>
            <span data-i18n="start-translation">开始翻译</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2>状态消息测试</h2>
        <div class="test-item">
            <strong>翻译完成：</strong>
            <span data-i18n="translation-completed">翻译完成！</span>
        </div>
        <div class="test-item">
            <strong>下载链接：</strong>
            <span data-i18n="download-bilingual">下载双语版本</span>
        </div>
        <div class="test-item">
            <strong>错误信息：</strong>
            <span data-i18n="error">错误：</span>
        </div>
    </div>

    <script>
        // 多语言支持 - 与主应用相同的翻译数据
        const translations = {
            zh: {
                'page-title': '双语书籍翻译服务',
                'main-title': '双语书籍翻译服务',
                'main-subtitle': '将您的书籍转换为双语版本，支持多种语言和翻译风格',
                'upload-title': '拖拽文件到此处或点击选择',
                'upload-subtitle': '支持 TXT、EPUB 格式，最大 100MB',
                'selected-file': '已选择文件：',
                'file-size': '文件大小：',
                'target-language': '目标语言',
                'translation-style': '翻译风格',
                'style-casual': '口语化 - 轻松自然的表达',
                'style-faithful': '严谨忠实 - 保持原文结构',
                'start-translation': '开始翻译',
                'translation-completed': '翻译完成！',
                'download-bilingual': '下载双语版本',
                'error': '错误：'
            },
            en: {
                'page-title': 'Bilingual Book Translation Service',
                'main-title': 'Bilingual Book Translation Service',
                'main-subtitle': 'Convert your books to bilingual versions with support for multiple languages and translation styles',
                'upload-title': 'Drag files here or click to select',
                'upload-subtitle': 'Supports TXT, EPUB formats, max 100MB',
                'selected-file': 'Selected file:',
                'file-size': 'File size:',
                'target-language': 'Target Language',
                'translation-style': 'Translation Style',
                'style-casual': 'Casual - Natural and relaxed expression',
                'style-faithful': 'Faithful - Maintain original structure',
                'start-translation': 'Start Translation',
                'translation-completed': 'Translation completed!',
                'download-bilingual': 'Download Bilingual Version',
                'error': 'Error: '
            }
        };
        
        let currentLanguage = 'zh';
        
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // 更新按钮状态
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`lang-${lang}`).classList.add('active');
            
            // 更新页面文本
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.getAttribute('data-i18n');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
            
            // 更新页面标题
            document.title = translations[lang]['page-title'];
            
            // 更新HTML lang属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            console.log(`语言已切换到: ${lang}`);
        }
    </script>
</body>
</html>
