#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试os导入修复
"""

import os
import sys
import uuid
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.utils.task_queue import TaskQueue
from src.models.translation_job import TranslationJob

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_os_import_in_task_queue():
    """测试任务队列中的os模块使用"""
    print("\n🔧 测试任务队列中的os模块使用")
    print("=" * 50)
    
    try:
        # 创建任务队列实例
        queue = TaskQueue()
        logger.info("任务队列创建成功")
        
        # 测试_process_job方法中的os.path.splitext调用
        # 创建一个测试任务
        job_id = str(uuid.uuid4())
        job = TranslationJob(
            job_id=job_id,
            upload_id="test-upload",
            original_filename="test_file.txt",
            file_path="test_sample.txt",
            target_language="中文",
            style="casual",
            status="pending"
        )
        job.save()
        logger.info(f"创建测试任务: {job_id}")
        
        # 测试文件名处理逻辑（模拟_process_job中的代码）
        test_filename = "test_document.pdf"
        base_filename = f"{os.path.splitext(test_filename)[0]}_bilingual"
        logger.info(f"文件名处理测试: {test_filename} -> {base_filename}")
        
        if base_filename == "test_document_bilingual":
            logger.info("✅ os.path.splitext 调用成功")
            result = True
        else:
            logger.error("❌ os.path.splitext 调用失败")
            result = False
        
        # 清理测试数据
        try:
            os.remove(f'data/jobs/{job_id}.json')
        except:
            pass
        
        print("✅ os模块使用测试通过" if result else "❌ os模块使用测试失败")
        return result
        
    except Exception as e:
        logger.error(f"os模块使用测试失败: {str(e)}")
        print("❌ os模块使用测试失败")
        return False

def test_task_queue_import():
    """测试任务队列导入"""
    print("\n📦 测试任务队列导入")
    print("=" * 50)
    
    try:
        # 测试导入任务队列相关模块
        from src.utils.task_queue import TaskQueue, start_task_queue, add_translation_job
        logger.info("✅ 任务队列模块导入成功")
        
        # 测试创建实例
        queue = TaskQueue()
        logger.info("✅ 任务队列实例创建成功")
        
        print("✅ 任务队列导入测试通过")
        return True
        
    except Exception as e:
        logger.error(f"任务队列导入测试失败: {str(e)}")
        print("❌ 任务队列导入测试失败")
        return False

def test_file_path_operations():
    """测试文件路径操作"""
    print("\n📁 测试文件路径操作")
    print("=" * 50)
    
    try:
        # 测试各种文件名的处理
        test_cases = [
            ("document.pdf", "document_bilingual"),
            ("book.epub", "book_bilingual"),
            ("text.txt", "text_bilingual"),
            ("novel.docx", "novel_bilingual"),
            ("file_with_dots.test.txt", "file_with_dots.test_bilingual"),
        ]
        
        all_passed = True
        for original, expected in test_cases:
            result = f"{os.path.splitext(original)[0]}_bilingual"
            if result == expected:
                logger.info(f"✅ {original} -> {result}")
            else:
                logger.error(f"❌ {original} -> {result} (期望: {expected})")
                all_passed = False
        
        print("✅ 文件路径操作测试通过" if all_passed else "❌ 文件路径操作测试失败")
        return all_passed
        
    except Exception as e:
        logger.error(f"文件路径操作测试失败: {str(e)}")
        print("❌ 文件路径操作测试失败")
        return False

def main():
    """主测试函数"""
    print("🔧 os导入修复验证")
    print("=" * 60)
    
    # 确保目录存在
    os.makedirs('data/jobs', exist_ok=True)
    
    # 运行测试
    tests = [
        ("任务队列导入", test_task_queue_import),
        ("文件路径操作", test_file_path_operations),
        ("任务队列中的os模块使用", test_os_import_in_task_queue),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            logger.error(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！os导入问题已修复")
        print("💡 现在可以正常运行翻译服务了")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
