#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译功能的用户认证集成
"""

import requests
import json
import time
import io

def login_user():
    """登录用户获取token"""
    print("🔑 登录用户...")
    
    url = "http://localhost:5000/api/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "test123456"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 登录成功，当前积分: {result['user']['points_balance']}")
            return result.get('token'), result['user']['points_balance']
        else:
            print(f"❌ 登录失败: {response.json()}")
            return None, 0
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None, 0

def upload_test_file():
    """上传测试文件"""
    print("\n📁 上传测试文件...")
    
    # 创建一个简单的测试文件内容
    test_content = """Hello World!
This is a test document for translation.
It contains multiple sentences to test the translation system.
The system should calculate the word count and deduct points accordingly.
This file has approximately 30 words for testing purposes."""
    
    url = "http://localhost:5000/api/upload"
    
    try:
        files = {'file': ('test_translation.txt', test_content, 'text/plain')}
        response = requests.post(url, files=files, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 文件上传成功:")
            print(f"   文件名: {result['filename']}")
            print(f"   文件大小: {result['file_size']} bytes")
            print(f"   Upload ID: {result['upload_id']}")
            return result['upload_id']
        else:
            print(f"❌ 文件上传失败: {response.json()}")
            return None
            
    except Exception as e:
        print(f"❌ 文件上传请求失败: {str(e)}")
        return None

def test_translation_without_auth(upload_id):
    """测试未登录状态下的翻译"""
    print("\n🚫 测试未登录翻译...")
    
    url = "http://localhost:5000/api/translate"
    data = {
        "upload_id": upload_id,
        "target_language": "中文",
        "style": "casual"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 401:
            result = response.json()
            print(f"✅ 正确拒绝未登录用户: {result.get('error')}")
            print(f"   所需积分: {result.get('required_points')}")
            print(f"   文件字数: {result.get('word_count')}")
        else:
            print(f"❌ 应该拒绝未登录用户，但状态码是: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 翻译请求失败: {str(e)}")

def test_translation_with_auth(upload_id, token):
    """测试登录状态下的翻译"""
    print("\n✅ 测试登录用户翻译...")
    
    url = "http://localhost:5000/api/translate"
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "upload_id": upload_id,
        "target_language": "中文",
        "style": "casual"
    }
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 翻译任务创建成功:")
            print(f"   任务ID: {result.get('job_id')}")
            print(f"   状态: {result.get('status')}")
            return result.get('job_id')
        else:
            result = response.json()
            print(f"❌ 翻译失败: {result.get('error')}")
            if 'required_points' in result:
                print(f"   所需积分: {result['required_points']}")
                print(f"   当前积分: {result.get('current_points', 'N/A')}")
            return None
            
    except Exception as e:
        print(f"❌ 翻译请求失败: {str(e)}")
        return None

def check_user_points_after_translation(token):
    """检查翻译后的用户积分"""
    print("\n💰 检查翻译后积分余额...")
    
    url = "http://localhost:5000/api/auth/me"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            result = response.json()
            points = result['user']['points_balance']
            print(f"✅ 当前积分余额: {points}")
            return points
        else:
            print(f"❌ 获取用户信息失败: {response.json()}")
            return None
    except Exception as e:
        print(f"❌ 获取用户信息请求失败: {str(e)}")
        return None

def test_insufficient_points(token):
    """测试积分不足的情况"""
    print("\n⚠️ 测试积分不足情况...")
    
    # 先检查当前积分
    current_points = check_user_points_after_translation(token)
    if current_points is None:
        print("❌ 无法获取当前积分")
        return
    
    print(f"当前积分: {current_points}")
    
    # 上传一个大文件来消耗积分
    large_content = "This is a test. " * 200  # 约600词，需要12积分
    
    url = "http://localhost:5000/api/upload"
    try:
        files = {'file': ('large_test.txt', large_content, 'text/plain')}
        response = requests.post(url, files=files, timeout=10)
        
        if response.status_code == 200:
            upload_id = response.json()['upload_id']
            
            # 尝试翻译大文件
            translate_url = "http://localhost:5000/api/translate"
            headers = {"Authorization": f"Bearer {token}"}
            data = {
                "upload_id": upload_id,
                "target_language": "中文",
                "style": "casual"
            }
            
            translate_response = requests.post(translate_url, json=data, headers=headers, timeout=10)
            
            if translate_response.status_code == 400:
                result = translate_response.json()
                if "积分不足" in result.get('error', ''):
                    print(f"✅ 正确检测积分不足: {result['error']}")
                    print(f"   所需积分: {result.get('required_points')}")
                    print(f"   当前积分: {result.get('current_points')}")
                else:
                    print(f"❌ 错误信息不正确: {result.get('error')}")
            else:
                print(f"❌ 应该返回积分不足错误，但状态码是: {translate_response.status_code}")
                
    except Exception as e:
        print(f"❌ 积分不足测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 翻译功能用户认证集成测试")
    print("=" * 60)
    
    # 1. 登录用户
    token, initial_points = login_user()
    if not token:
        print("❌ 无法登录，跳过测试")
        return
    
    # 2. 上传测试文件
    upload_id = upload_test_file()
    if not upload_id:
        print("❌ 文件上传失败，跳过测试")
        return
    
    # 3. 测试未登录翻译
    test_translation_without_auth(upload_id)
    
    # 4. 测试登录用户翻译
    job_id = test_translation_with_auth(upload_id, token)
    
    # 5. 检查积分扣除
    if job_id:
        final_points = check_user_points_after_translation(token)
        if final_points is not None and initial_points is not None:
            deducted = initial_points - final_points
            print(f"💸 积分扣除: {deducted} 积分")
    
    # 6. 测试积分不足情况
    test_insufficient_points(token)
    
    print("\n✅ 翻译认证集成测试完成！")
    print("\n📊 测试总结:")
    print("1. ✅ 未登录用户被正确拒绝")
    print("2. ✅ 登录用户可以正常翻译")
    print("3. ✅ 积分正确扣除")
    print("4. ✅ 积分不足时正确提示")

if __name__ == "__main__":
    main()
