#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查PDF文件内容
"""

import PyPDF2
import os

def check_pdf(pdf_path):
    """检查PDF文件内容"""
    if not os.path.exists(pdf_path):
        print(f"文件不存在: {pdf_path}")
        return
    
    try:
        with open(pdf_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            print(f"PDF文件: {pdf_path}")
            print(f"页数: {len(reader.pages)}")
            
            if reader.pages:
                text = reader.pages[0].extract_text()
                print(f"第一页文本长度: {len(text)} 字符")
                print("第一页内容:")
                print("-" * 40)
                print(text[:500])
                print("-" * 40)
                
                # 检查中文字符
                chinese_chars = [c for c in text if ord(c) > 127]
                print(f"中文字符数量: {len(chinese_chars)}")
                
                # 检查方块字符
                black_squares = text.count('■')
                print(f"方块字符(■)数量: {black_squares}")
                
                if black_squares > 0:
                    print("⚠️  发现方块字符，中文字体可能有问题")
                else:
                    print("✅ 没有发现方块字符")
                    
    except Exception as e:
        print(f"读取PDF失败: {str(e)}")

if __name__ == "__main__":
    check_pdf("test_fixed_output/test_fixed.pdf")
